import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

/// Service class for Firebase initialization and configuration
class FirebaseService {
  static FirebaseAuth get auth => FirebaseAuth.instance;
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  static FirebaseAnalytics get analytics => FirebaseAnalytics.instance;

  /// Initialize Firebase services
  static Future<void> initialize() async {
    try {
      await Firebase.initializeApp();
      await _configureFirestore();
      await _configureAuth();
    } catch (e) {
      throw Exception('Failed to initialize Firebase: $e');
    }
  }

  /// Configure Firestore settings
  static Future<void> _configureFirestore() async {
    // Enable offline persistence
    firestore.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
  }

  /// Configure Firebase Auth settings
  static Future<void> _configureAuth() async {
    // Configure auth state persistence
    await auth.setPersistence(Persistence.LOCAL);
  }

  /// Check if user is authenticated
  static bool get isAuthenticated => auth.currentUser != null;

  /// Get current user
  static User? get currentUser => auth.currentUser;

  /// Get current user ID
  static String? get currentUserId => auth.currentUser?.uid;
}
