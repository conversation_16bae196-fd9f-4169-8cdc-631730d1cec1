import '../models/session_model.dart';

/// Abstract repository interface for session operations
abstract class SessionRepository {
  /// Get session by ID
  Future<SessionModel?> getSessionById(String id);

  /// Create new session
  Future<void> createSession(SessionModel session);

  /// Update existing session
  Future<void> updateSession(SessionModel session);

  /// Delete session
  Future<void> deleteSession(String id);

  /// Get sessions by leader ID
  Future<List<SessionModel>> getSessionsByLeader(String leaderId);

  /// Get sessions by area ID
  Future<List<SessionModel>> getSessionsByArea(String areaId);

  /// Get session by invite code
  Future<SessionModel?> getSessionByInviteCode(String inviteCode);

  /// Get active sessions
  Future<List<SessionModel>> getActiveSessions();

  /// Get all sessions
  Future<List<SessionModel>> getAllSessions();

  /// Get sessions that haven't been synced to cloud
  Future<List<SessionModel>> getUnsyncedSessions();

  /// Mark session as synced
  Future<void> markSessionAsSynced(String id);
}
