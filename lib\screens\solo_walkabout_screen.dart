import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/area_model.dart';
import '../models/session_model.dart';
import '../models/checklist_item_model.dart';
import '../models/finding_model.dart';
import '../providers/auth_provider.dart';
import '../repositories/repository_factory.dart';
import '../utils/app_theme.dart';
import 'checklist_screen.dart';
import 'hazard_form_screen.dart';

/// Screen for conducting solo walkabout inspection
class SoloWalkaboutScreen extends StatefulWidget {
  final AreaModel area;

  const SoloWalkaboutScreen({
    super.key,
    required this.area,
  });

  @override
  State<SoloWalkaboutScreen> createState() => _SoloWalkaboutScreenState();
}

class _SoloWalkaboutScreenState extends State<SoloWalkaboutScreen> {
  final RepositoryFactory _repositoryFactory = RepositoryFactory();
  SessionModel? _currentSession;
  List<ChecklistItemModel> _checklistItems = [];
  List<FindingModel> _hazards = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _createSession();
  }

  Future<void> _createSession() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create new session
      final session = SessionModel(
        id: const Uuid().v4(),
        type: SessionType.inspection,
        leaderId: user.id,
        areaId: widget.area.id,
        status: SessionStatus.active,
        inviteCode: '', // Not needed for solo sessions
        createdAt: DateTime.now(),
      );

      final sessionRepository = _repositoryFactory.getSessionRepository();
      await sessionRepository.createSession(session);

      setState(() {
        _currentSession = session;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Solo Walkabout - ${widget.area.name}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _currentSession == null
              ? _buildErrorState()
              : _buildContent(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Failed to create session',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please try again',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _createSession,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Session info card
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Inspection Session',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.location_on,
                          size: 20, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text('Area: ${widget.area.name}'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.access_time,
                          size: 20, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(
                          'Started: ${_formatDateTime(_currentSession!.createdAt)}'),
                    ],
                  ),
                  if (widget.area.description != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.info_outline,
                            size: 20, color: Colors.grey),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(widget.area.description!),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Inspection actions
          Text(
            'Inspection Tasks',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),

          const SizedBox(height: 16),

          // Checklist card
          _buildActionCard(
            title: 'Safety Checklist',
            subtitle: 'Complete 10-point safety inspection checklist',
            icon: Icons.checklist,
            color: AppTheme.primaryColor,
            completed: _checklistItems.isNotEmpty,
            onTap: _startChecklist,
          ),

          const SizedBox(height: 12),

          // Hazard reporting card
          _buildActionCard(
            title: 'Report Hazards',
            subtitle:
                'Document safety hazards and concerns (${_hazards.length} reported)',
            icon: Icons.warning,
            color: Colors.orange,
            completed: _hazards.isNotEmpty,
            onTap: _reportHazard,
          ),

          const Spacer(),

          // Complete session button
          if (_checklistItems.isNotEmpty || _hazards.isNotEmpty)
            ElevatedButton(
              onPressed: _completeSession,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Complete Inspection',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool completed,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),
              if (completed)
                const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 24,
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[400],
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _startChecklist() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ChecklistScreen(
          sessionId: _currentSession!.id,
          areaName: widget.area.name,
          onChecklistCompleted: (items) {
            setState(() {
              _checklistItems = items;
            });
          },
        ),
      ),
    );
  }

  void _reportHazard() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user == null || _currentSession == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => HazardFormScreen(
          sessionId: _currentSession!.id,
          areaName: widget.area.name,
          onHazardSaved: (hazard) {
            setState(() {
              // Set the author ID
              final updatedHazard = hazard.copyWith(authorId: user.id);
              _hazards.add(updatedHazard);
            });
          },
        ),
      ),
    );
  }

  Future<void> _completeSession() async {
    if (_currentSession == null) return;

    try {
      // Update session status to completed
      final completedSession = _currentSession!.copyWith(
        status: SessionStatus.completed,
        completedAt: DateTime.now(),
      );

      final sessionRepository = _repositoryFactory.getSessionRepository();
      await sessionRepository.updateSession(completedSession);

      // Save all hazards/findings
      if (_hazards.isNotEmpty) {
        final findingRepository = _repositoryFactory.getFindingRepository();
        for (final hazard in _hazards) {
          await findingRepository.createFinding(hazard);
        }
      }

      // Save checklist data
      if (_checklistItems.isNotEmpty) {
        final checklistRepository = _repositoryFactory.getChecklistRepository();
        await checklistRepository.saveChecklist(
            completedSession.id, _checklistItems);
      }

      // Show completion dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Inspection Complete'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                    'Your inspection has been completed and saved locally.'),
                const SizedBox(height: 16),
                Text('Summary:',
                    style: Theme.of(context)
                        .textTheme
                        .titleSmall
                        ?.copyWith(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                if (_checklistItems.isNotEmpty)
                  Text(
                      '• Checklist: ${_checklistItems.length} items completed'),
                if (_hazards.isNotEmpty)
                  Text('• Hazards: ${_hazards.length} hazards reported'),
                const SizedBox(height: 8),
                const Text(
                    'Data will be synced to the cloud when internet is available.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Return to area selection
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete session: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
