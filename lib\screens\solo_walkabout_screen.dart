import 'package:flutter/material.dart';
import '../models/area_model.dart';
import '../utils/app_theme.dart';

/// Screen for conducting solo walkabout inspection
class SoloWalkaboutScreen extends StatefulWidget {
  final AreaModel area;

  const SoloWalkaboutScreen({
    super.key,
    required this.area,
  });

  @override
  State<SoloWalkaboutScreen> createState() => _SoloWalkaboutScreenState();
}

class _SoloWalkaboutScreenState extends State<SoloWalkaboutScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Solo Walkabout - ${widget.area.name}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.construction,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Solo Walkabout Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will be implemented in the next task',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
