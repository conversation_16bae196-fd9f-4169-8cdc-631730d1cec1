# Project Structure

```
lib/
├── main.dart                 # App entry point
├── app/
│   ├── app.dart             # Main app widget
│   ├── routes.dart          # Route definitions
│   └── theme.dart           # App theme configuration
├── core/
│   ├── constants/           # App constants
│   ├── errors/              # Error handling
│   ├── network/             # Network utilities
│   ├── storage/             # Local storage utilities
│   └── utils/               # Helper utilities
├── features/
│   ├── auth/
│   │   ├── data/            # Auth data sources
│   │   ├── domain/          # Auth business logic
│   │   └── presentation/    # Auth UI components
│   ├── inspection/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── collaboration/
│   └── reporting/
├── shared/
│   ├── widgets/             # Reusable UI components
│   ├── models/              # Data models
│   └── services/            # Shared services
└── generated/               # Generated code
```
