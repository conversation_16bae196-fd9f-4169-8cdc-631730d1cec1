/// Site data model
class SiteModel {
  final String id;
  final String name;
  final String address;
  final String? ownerId;
  final String? description;
  final double? latitude;
  final double? longitude;
  final DateTime createdAt;

  const SiteModel({
    required this.id,
    required this.name,
    required this.address,
    this.ownerId,
    this.description,
    this.latitude,
    this.longitude,
    required this.createdAt,
  });

  /// Create SiteModel from map
  factory SiteModel.fromMap(Map<String, dynamic> map) {
    return SiteModel(
      id: map['id'] as String,
      name: map['name'] as String,
      address: map['address'] as String,
      ownerId: map['owner_id'] as String?,
      description: map['description'] as String?,
      latitude: map['latitude'] as double?,
      longitude: map['longitude'] as double?,
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert SiteModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'owner_id': ownerId,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  SiteModel copyWith({
    String? id,
    String? name,
    String? address,
    String? ownerId,
    String? description,
    double? latitude,
    double? longitude,
    DateTime? createdAt,
  }) {
    return SiteModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      ownerId: ownerId ?? this.ownerId,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SiteModel &&
        other.id == id &&
        other.name == name &&
        other.address == address &&
        other.ownerId == ownerId &&
        other.description == description &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        address.hashCode ^
        ownerId.hashCode ^
        description.hashCode ^
        latitude.hashCode ^
        longitude.hashCode ^
        createdAt.hashCode;
  }

  @override
  String toString() {
    return 'SiteModel(id: $id, name: $name, address: $address, ownerId: $ownerId, description: $description, latitude: $latitude, longitude: $longitude, createdAt: $createdAt)';
  }
}
