import 'package:flutter/material.dart';
import '../models/user_model.dart';

/// Role selection card widget with WCAG-compliant design
class Role<PERSON>ard extends StatelessWidget {
  final UserRole role;
  final bool isSelected;
  final VoidCallback onTap;

  const RoleCard({
    super.key,
    required this.role,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primaryContainer
              : colorScheme.surface,
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary
                : colorScheme.outline,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: colorScheme.primary.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Role Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getRoleIcon(),
                    size: 24,
                    color: isSelected
                        ? colorScheme.onPrimary
                        : colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(width: 16),
                
                // Role Title and Selection Indicator
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getRoleTitle(),
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? colorScheme.onPrimaryContainer
                              : colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getRoleSubtitle(),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isSelected
                              ? colorScheme.onPrimaryContainer.withOpacity(0.8)
                              : colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Selection Indicator
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected
                        ? colorScheme.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.outline,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          size: 16,
                          color: colorScheme.onPrimary,
                        )
                      : null,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Role Description
            Text(
              _getRoleDescription(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected
                    ? colorScheme.onPrimaryContainer
                    : colorScheme.onSurface,
                height: 1.4,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Role Capabilities
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _getRoleCapabilities()
                  .map((capability) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              size: 16,
                              color: isSelected
                                  ? colorScheme.primary
                                  : colorScheme.primary.withOpacity(0.7),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                capability,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isSelected
                                      ? colorScheme.onPrimaryContainer
                                      : colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getRoleIcon() {
    switch (role) {
      case UserRole.leader:
        return Icons.supervisor_account;
      case UserRole.observer:
        return Icons.visibility;
    }
  }

  String _getRoleTitle() {
    switch (role) {
      case UserRole.leader:
        return 'Safety Leader';
      case UserRole.observer:
        return 'Safety Observer';
    }
  }

  String _getRoleSubtitle() {
    switch (role) {
      case UserRole.leader:
        return 'Lead safety inspections';
      case UserRole.observer:
        return 'Participate in inspections';
    }
  }

  String _getRoleDescription() {
    switch (role) {
      case UserRole.leader:
        return 'As a Safety Leader, you can create and manage safety inspection sessions, assign tasks to team members, and generate comprehensive reports.';
      case UserRole.observer:
        return 'As a Safety Observer, you can participate in safety inspections, document findings, and contribute to team safety assessments.';
    }
  }

  List<String> _getRoleCapabilities() {
    switch (role) {
      case UserRole.leader:
        return [
          'Create and manage inspection sessions',
          'Assign tasks to team members',
          'Generate detailed safety reports',
          'Access advanced analytics',
          'Manage team permissions',
        ];
      case UserRole.observer:
        return [
          'Join inspection sessions',
          'Document safety findings',
          'Take photos and notes',
          'View team reports',
          'Collaborate with team members',
        ];
    }
  }
}

/// Extension to get role display information
extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.leader:
        return 'Safety Leader';
      case UserRole.observer:
        return 'Safety Observer';
    }
  }

  String get description {
    switch (this) {
      case UserRole.leader:
        return 'Lead and manage safety inspections';
      case UserRole.observer:
        return 'Participate in safety inspections';
    }
  }

  IconData get icon {
    switch (this) {
      case UserRole.leader:
        return Icons.supervisor_account;
      case UserRole.observer:
        return Icons.visibility;
    }
  }

  Color getColor(ColorScheme colorScheme) {
    switch (this) {
      case UserRole.leader:
        return colorScheme.primary;
      case UserRole.observer:
        return colorScheme.secondary;
    }
  }
}
