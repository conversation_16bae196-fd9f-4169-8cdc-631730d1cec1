# State Management

## Provider Pattern Implementation

```dart
// State model
class InspectionState extends ChangeNotifier {
  List<Finding> _findings = [];
  bool _isLoading = false;
  String? _error;

  List<Finding> get findings => List.unmodifiable(_findings);
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> addFinding(Finding finding) async {
    _setLoading(true);
    try {
      await _repository.saveFinding(finding);
      _findings.add(finding);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
}

// Provider setup
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => AuthState()),
    ChangeNotifierProvider(create: (_) => InspectionState()),
    ChangeNotifierProvider(create: (_) => SyncState()),
  ],
  child: SafeStrideApp(),
)
```

## Store Structure Template

```dart
abstract class BaseState extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() => _setError(null);
}
```
