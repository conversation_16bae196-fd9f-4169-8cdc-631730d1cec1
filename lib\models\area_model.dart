/// Area data model
class AreaModel {
  final String id;
  final String name;
  final String siteId;
  final String? description;
  final String? qrCode;
  final DateTime createdAt;

  const AreaModel({
    required this.id,
    required this.name,
    required this.siteId,
    this.description,
    this.qrCode,
    required this.createdAt,
  });

  /// Create AreaModel from map
  factory AreaModel.fromMap(Map<String, dynamic> map) {
    return AreaModel(
      id: map['id'] as String,
      name: map['name'] as String,
      siteId: map['site_id'] as String,
      description: map['description'] as String?,
      qrCode: map['qr_code'] as String?,
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert AreaModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'site_id': siteId,
      'description': description,
      'qr_code': qrCode,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  AreaModel copyWith({
    String? id,
    String? name,
    String? siteId,
    String? description,
    String? qrCode,
    DateTime? createdAt,
  }) {
    return AreaModel(
      id: id ?? this.id,
      name: name ?? this.name,
      siteId: siteId ?? this.siteId,
      description: description ?? this.description,
      qrCode: qrCode ?? this.qrCode,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AreaModel &&
        other.id == id &&
        other.name == name &&
        other.siteId == siteId &&
        other.description == description &&
        other.qrCode == qrCode &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        siteId.hashCode ^
        description.hashCode ^
        qrCode.hashCode ^
        createdAt.hashCode;
  }

  @override
  String toString() {
    return 'AreaModel(id: $id, name: $name, siteId: $siteId, description: $description, qrCode: $qrCode, createdAt: $createdAt)';
  }
}
