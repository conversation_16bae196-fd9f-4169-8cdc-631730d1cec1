# SafeStride Technical Architecture

**Version:** 1.0  
**Last Updated:** 2025-01-28

---

## Overview
SafeStride is an offline-first, cross-platform mobile application built with Flutter. The backend leverages Google Firebase for authentication, real-time data storage (Firestore), analytics, hosting, and BigQuery export. Local persistence and offline functionality are powered by an encrypted SQLite database. On-device AI (TFLite) delivers hazard tagging and duplicate detection while respecting privacy and performance constraints.

This document details implementation-ready architecture elements that address the *practical & essential* solutions identified during technical stack optimisation.

---

## Key Components
| # | Component | Technology | Purpose |
|---|-----------|------------|---------|
| 1 | Mobile App | Flutter (Dart) | Cross-platform UI & business logic |
| 2 | Local DB | SQLite + Drift ORM | Offline cache, sync queue, audit logs |
| 3 | Cloud Backend | Firebase Auth / Firestore / Storage | Secure user management & cloud persistence |
| 4 | AI Models | TFLite (MobileNetV2, MiniLM) | On-device photo tagging & duplicate detection |
| 5 | Functions (optional) | Cloud Functions | Server-side data sanitisation & BigQuery export |
| 6 | Analytics | Firebase Analytics + Crashlytics | Usage insights & error reporting |
| 7 | CI/CD | GitHub Actions → Firebase App Distribution | Automated build, test & deploy |

---

## Offline Sync Logic
1. **Change Tracking**  
   • All local mutations (insert/update/delete) are wrapped in a `SyncTransaction` helper that writes an entry to the **sync_queue** table with:
   `{id, table, row_id, action, payload_json, ts, retries}`
2. **Sync Worker**  
   • A background isolate checks connectivity every 15 s.  
   • When online, it dequeues items, batches up to 500 writes, and calls Firestore in a single commit.  
   • On success ➜ row removed.  On failure ➜ `retries++`, exponential back-off.
3. **Conflict Resolution**  
   • For simple entities (findings, checklist items) use *last-write-wins* by comparing `updated_at`.  
   • For complex entities (hazards) mark conflicts and surface merge UI later (Phase 3).
4. **Error Feedback**  
   • App-wide `SyncStatusCubit` exposes states: `synced`, `queued`, `syncing`, `failed`.  
   • Small banner on Home & Review Hub shows current status.

### Data Flow Diagram (Simplified)
```
User Action → Local DB (Drift) → sync_queue → [Worker] → Firestore
                                          ↑                ↓
                                 Connectivity Monitor ← Firebase
```

---

## AI Model Update Mechanism
1. **Metadata Doc** (`ai/models/{name}`) stores `{latest_version, url, checksum}`.  
2. On app start → fetch metadata; compare with local `SharedPreferences`.  
3. If newer → download model to `app_doc_dir/models/`, validate checksum, update pointer.
4. Failed download ➜ fallback to current model.

---

## Image Handling & Firestore Optimisation
- **Compression:** max 250 KB JPEG via `image_picker` + `flutter_image_compress` (80 % quality, 1600 px max).
- **Batched Writes:** use `WriteBatch`/`runTransaction` in Firestore service layer.  
- **Listener Scoping:** subscribe only to documents needed for visible UI (e.g., current session).  
- **Archiving:** Cloud Function runs nightly ➜ moves sessions > 3 years to Coldline Storage & deletes thumbnails to save reads.

---

## Security & Privacy Controls
- **Firebase App Check** (DeviceCheck / Play Integrity) required for read/write.  
- **Encrypted SQLite** via `sqlcipher` build-tag.  
- **Role-based Rules:** Observers may only read/write own findings; Leaders full session scope.  
- **In-App Data Controls:** Settings ➜ *Export My Data* (CSV) & *Delete My Account* (calls Auth delete + Firestore purge Cloud Function).

---

## CI/CD Pipeline (GitHub Actions)
1. **Lint & Test:** `flutter analyze`, `flutter test`.  
2. **Build:** `flutter build apk --release` & `flutter build ipa`.  
3. **Device Tests:** Firebase Test Lab matrix (Pixel 4a, iPhone 8).  
4. **Distribute:** Upload to Firebase App Distribution on `main` tags.

---

## Monitoring & Alerts
- **Crashlytics** for runtime errors.  
- **Firestore Usage Alerts** at 50 %, 75 %, 90 % of free tier/write quota.  
- **Sync Queue Dashboard:** simple `SELECT COUNT(*) WHERE retries>0` metric to highlight stuck items.

---

*This architecture section is implementation-ready and maps directly to tasks in the workflow plan.*
