# Routing

## Route Configuration
```dart
final GoRouter router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/auth',
      builder: (context, state) => const AuthScreen(),
    ),
    GoRoute(
      path: '/role-selection',
      builder: (context, state) => const RoleSelectionScreen(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomeScreen(),
      routes: [
        GoRoute(
          path: 'inspection/:sessionId',
          builder: (context, state) => InspectionScreen(
            sessionId: state.pathParameters['sessionId']!,
          ),
        ),
      ],
    ),
  ],
  redirect: (context, state) {
    final authState = context.read<AuthState>();
    final isLoggedIn = authState.isAuthenticated;
    final hasRole = authState.user?.role != null;

    if (!isLoggedIn && state.location != '/auth') {
      return '/auth';
    }
    if (isLoggedIn && !hasRole && state.location != '/role-selection') {
      return '/role-selection';
    }
    return null;
  },
);
```
