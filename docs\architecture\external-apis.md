# External APIs

### Firebase Services
**Purpose:** Primary backend infrastructure
**Authentication:** Firebase Admin SDK, API keys
**Key Endpoints:**
- Authentication: Firebase Auth REST API
- Database: Firestore REST API
- Storage: Cloud Storage API
**Rate Limits:** Firebase quotas (Spark: 50K reads/day, Blaze: pay-per-use)

### Device APIs
**Purpose:** Native mobile capabilities
**Key Integrations:**
- Camera API (photo capture)
- File System API (local storage)
- Network API (connectivity detection)
- Notification API (local notifications)

