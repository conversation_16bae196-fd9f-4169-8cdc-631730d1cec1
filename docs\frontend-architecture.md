# SafeStride Frontend Architecture Document

## Introduction

This document details the Flutter frontend architecture for SafeStride, complementing the main Architecture Document. It focuses on mobile-specific patterns, offline-first UI design, and role-based user experiences for workplace safety inspections.

**Relationship to Main Architecture:**
This document builds upon the core technology stack and architectural patterns defined in the main Architecture Document, specifically addressing Flutter app structure, state management, and mobile UX patterns.

## Framework Selection

### Flutter Framework Choice
**Selected Framework:** Flutter 3.16.0 with Dart 3.2.0

**Rationale:**
- **Cross-platform efficiency:** Single codebase for iOS and Android reduces development effort for single developer
- **Offline-first support:** Excellent local storage and sync capabilities
- **Performance:** Near-native performance suitable for industrial environments
- **Ecosystem:** Rich plugin ecosystem for camera, QR codes, PDF generation
- **Target compatibility:** Supports iOS 12.0+ and Android 8.0+ requirements

## Frontend Tech Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|----------|
| **Framework** | Flutter | 3.16.0 | Cross-platform mobile development |
| **Language** | Dart | 3.2.0 | Primary development language |
| **State Management** | Provider | 6.1.1 | Application state management |
| **Local Database** | sqflite | 2.3.0 | Offline data storage |
| **Navigation** | go_router | 12.1.3 | Declarative routing |
| **Camera** | image_picker | 1.0.4 | Photo capture and selection |
| **QR Codes** | qr_flutter, qr_code_scanner | 4.1.0, 1.0.1 | QR generation and scanning |
| **PDF Generation** | pdf | 3.10.4 | Report generation |
| **Notifications** | flutter_local_notifications | 16.3.0 | Local notifications |
| **Connectivity** | connectivity_plus | 5.0.2 | Network status monitoring |
| **Secure Storage** | flutter_secure_storage | 9.0.0 | Credential storage |
| **AI/ML** | tflite_flutter | 0.10.0 | On-device AI inference |

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── app/
│   ├── app.dart             # Main app widget
│   ├── routes.dart          # Route definitions
│   └── theme.dart           # App theme configuration
├── core/
│   ├── constants/           # App constants
│   ├── errors/              # Error handling
│   ├── network/             # Network utilities
│   ├── storage/             # Local storage utilities
│   └── utils/               # Helper utilities
├── features/
│   ├── auth/
│   │   ├── data/            # Auth data sources
│   │   ├── domain/          # Auth business logic
│   │   └── presentation/    # Auth UI components
│   ├── inspection/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── collaboration/
│   └── reporting/
├── shared/
│   ├── widgets/             # Reusable UI components
│   ├── models/              # Data models
│   └── services/            # Shared services
└── generated/               # Generated code
```

## Component Standards

### Widget Template
```dart
class SafeStrideButton extends StatelessWidget {
  const SafeStrideButton({
    Key? key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.variant = ButtonVariant.primary,
  }) : super(key: key);

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final ButtonVariant variant;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: _getButtonStyle(context, variant),
      child: isLoading 
        ? const CircularProgressIndicator()
        : Text(text),
    );
  }
}
```

### Naming Conventions
- **Widgets:** PascalCase with descriptive names (InspectionCard, HazardForm)
- **Files:** snake_case matching widget names (inspection_card.dart)
- **Private methods:** _camelCase with underscore prefix
- **Constants:** SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE)

## State Management

### Provider Pattern Implementation

```dart
// State model
class InspectionState extends ChangeNotifier {
  List<Finding> _findings = [];
  bool _isLoading = false;
  String? _error;

  List<Finding> get findings => List.unmodifiable(_findings);
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> addFinding(Finding finding) async {
    _setLoading(true);
    try {
      await _repository.saveFinding(finding);
      _findings.add(finding);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }
}

// Provider setup
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => AuthState()),
    ChangeNotifierProvider(create: (_) => InspectionState()),
    ChangeNotifierProvider(create: (_) => SyncState()),
  ],
  child: SafeStrideApp(),
)
```

### Store Structure Template

```dart
abstract class BaseState extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() => _setError(null);
}
```

## API Integration

### Service Template
```dart
class InspectionService {
  final FirebaseFirestore _firestore;
  final LocalDatabase _localDb;
  final ConnectivityService _connectivity;

  Future<List<Finding>> getFindings(String sessionId) async {
    try {
      // Try local first for offline support
      final localFindings = await _localDb.getFindings(sessionId);
      
      if (await _connectivity.isConnected()) {
        // Sync with remote if online
        final remoteFindings = await _firestore
            .collection('sessions')
            .doc(sessionId)
            .collection('findings')
            .get();
        
        // Merge and update local cache
        await _syncFindings(localFindings, remoteFindings);
      }
      
      return localFindings;
    } catch (e) {
      throw InspectionException('Failed to load findings: $e');
    }
  }
}
```

### Client Configuration
```dart
class ApiClient {
  static const Duration _timeout = Duration(seconds: 30);
  static const int _maxRetries = 3;

  final FirebaseFirestore _firestore;
  final ConnectivityService _connectivity;

  Future<T> executeWithRetry<T>(Future<T> Function() operation) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        return await operation().timeout(_timeout);
      } catch (e) {
        if (attempt == _maxRetries || !_shouldRetry(e)) rethrow;
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
    throw Exception('Max retries exceeded');
  }
}
```

## Routing

### Route Configuration
```dart
final GoRouter router = GoRouter(
  initialLocation: '/splash',
  routes: [
    GoRoute(
      path: '/splash',
      builder: (context, state) => const SplashScreen(),
    ),
    GoRoute(
      path: '/auth',
      builder: (context, state) => const AuthScreen(),
    ),
    GoRoute(
      path: '/role-selection',
      builder: (context, state) => const RoleSelectionScreen(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomeScreen(),
      routes: [
        GoRoute(
          path: 'inspection/:sessionId',
          builder: (context, state) => InspectionScreen(
            sessionId: state.pathParameters['sessionId']!,
          ),
        ),
      ],
    ),
  ],
  redirect: (context, state) {
    final authState = context.read<AuthState>();
    final isLoggedIn = authState.isAuthenticated;
    final hasRole = authState.user?.role != null;

    if (!isLoggedIn && state.location != '/auth') {
      return '/auth';
    }
    if (isLoggedIn && !hasRole && state.location != '/role-selection') {
      return '/role-selection';
    }
    return null;
  },
);
```

## Styling Guidelines

### Design System Approach
```dart
class SafeStrideTheme {
  static ThemeData get lightTheme => ThemeData(
    primarySwatch: Colors.blue,
    scaffoldBackgroundColor: Colors.grey[50],
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
      elevation: 2,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(120, 48), // Large touch targets
        textStyle: const TextStyle(fontSize: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        color: Colors.black87,
      ),
    ),
  );
}
```

### Global Theme Variables
```dart
class SafeStrideColors {
  static const Color primary = Color(0xFF1976D2);
  static const Color secondary = Color(0xFF424242);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color surface = Color(0xFFFAFAFA);
}

class SafeStrideSizes {
  static const double touchTarget = 48.0;
  static const double borderRadius = 8.0;
  static const double spacing = 16.0;
  static const double iconSize = 24.0;
}
```

## Testing Requirements

### Component Test Template
```dart
void main() {
  group('InspectionCard Widget Tests', () {
    testWidgets('displays finding information correctly', (tester) async {
      final finding = Finding(
        id: '1',
        description: 'Test hazard',
        severity: Severity.high,
        category: 'Spill',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InspectionCard(finding: finding),
          ),
        ),
      );

      expect(find.text('Test hazard'), findsOneWidget);
      expect(find.text('High'), findsOneWidget);
      expect(find.text('Spill'), findsOneWidget);
    });

    testWidgets('handles tap events', (tester) async {
      bool tapped = false;
      final finding = Finding(id: '1', description: 'Test');

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InspectionCard(
              finding: finding,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(InspectionCard));
      expect(tapped, isTrue);
    });
  });
}
```

### Best Practices
- **Widget Tests:** Test UI behavior and user interactions
- **Integration Tests:** Test complete user flows
- **Golden Tests:** Visual regression testing for critical screens
- **Accessibility Tests:** Verify WCAG compliance
- **Performance Tests:** Monitor frame rates and memory usage

## Environment Configuration

```dart
class Environment {
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  static bool get isDevelopment => _environment == 'development';
  static bool get isStaging => _environment == 'staging';
  static bool get isProduction => _environment == 'production';

  static String get firebaseProjectId {
    switch (_environment) {
      case 'production':
        return 'safestride-prod';
      case 'staging':
        return 'safestride-staging';
      default:
        return 'safestride-dev';
    }
  }
}
```

## Frontend Developer Standards

### Critical Coding Rules
1. **Offline-First:** All core features must work without network connectivity
2. **Accessibility:** Minimum 48dp touch targets, high contrast colors, semantic labels
3. **Performance:** 60fps target, lazy loading for lists, image optimization
4. **Error Handling:** Graceful degradation with user-friendly error messages
5. **State Management:** Use Provider pattern consistently, avoid setState in complex widgets
6. **Testing:** Minimum 80% test coverage for business logic widgets
7. **Security:** Never store sensitive data in plain text, use secure storage
8. **Responsive Design:** Support various screen sizes and orientations

### Quick Reference
- **File Naming:** snake_case for files, PascalCase for classes
- **Widget Structure:** StatelessWidget preferred, StatefulWidget only when necessary
- **Error Boundaries:** Wrap async operations in try-catch blocks
- **Loading States:** Always show loading indicators for async operations
- **Navigation:** Use go_router for declarative navigation
- **Theming:** Use theme data consistently, avoid hardcoded colors/sizes

---

*This frontend architecture document provides the detailed implementation guidance for SafeStride's Flutter mobile application, ensuring consistency with the overall system architecture while addressing mobile-specific concerns.*