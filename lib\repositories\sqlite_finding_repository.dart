import 'package:sqflite/sqflite.dart';
import '../models/finding_model.dart';
import '../services/database_service.dart';
import 'finding_repository.dart';

/// SQLite implementation of FindingRepository
class SqliteFindingRepository implements FindingRepository {
  static const String _tableName = 'findings';

  @override
  Future<FindingModel?> getFindingById(String id) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return FindingModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<void> createFinding(FindingModel finding) async {
    final db = await DatabaseService.database;
    await db.insert(
      _tableName,
      finding.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> updateFinding(FindingModel finding) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      finding.toMap(),
      where: 'id = ?',
      whereArgs: [finding.id],
    );
  }

  @override
  Future<void> deleteFinding(String id) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<FindingModel>> getFindingsBySession(String sessionId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<List<FindingModel>> getFindingsByAuthor(String authorId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'author_id = ?',
      whereArgs: [authorId],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<List<FindingModel>> getFindingsBySeverity(String severity) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'severity = ?',
      whereArgs: [severity],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<List<FindingModel>> getFindingsByCategory(String category) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'category = ?',
      whereArgs: [category],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<List<FindingModel>> getAllFindings() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<List<FindingModel>> getUnsyncedFindings() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => FindingModel.fromMap(map)).toList();
  }

  @override
  Future<void> markFindingAsSynced(String id) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<int> getFindingsCountBySession(String sessionId) async {
    final db = await DatabaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE session_id = ?',
      [sessionId],
    );
    return result.first['count'] as int;
  }

  @override
  Future<Map<String, int>> getFindingsCountBySeverity(String sessionId) async {
    final db = await DatabaseService.database;
    final result = await db.rawQuery('''
      SELECT severity, COUNT(*) as count 
      FROM $_tableName 
      WHERE session_id = ? 
      GROUP BY severity
    ''', [sessionId]);
    
    final Map<String, int> counts = {};
    for (final row in result) {
      counts[row['severity'] as String] = row['count'] as int;
    }
    return counts;
  }
}
