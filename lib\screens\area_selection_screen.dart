import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/area_model.dart';
import '../models/site_model.dart';
import '../providers/auth_provider.dart';
import '../repositories/repository_factory.dart';
import '../utils/app_theme.dart';
import 'solo_walkabout_screen.dart';

/// Screen for selecting area and managing areas for solo walkabout
class AreaSelectionScreen extends StatefulWidget {
  const AreaSelectionScreen({super.key});

  @override
  State<AreaSelectionScreen> createState() => _AreaSelectionScreenState();
}

class _AreaSelectionScreenState extends State<AreaSelectionScreen> {
  final RepositoryFactory _repositoryFactory = RepositoryFactory();

  List<SiteModel> _sites = [];
  List<AreaModel> _areas = [];
  SiteModel? _selectedSite;
  AreaModel? _selectedArea;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      final siteRepository = _repositoryFactory.getSiteRepository();
      final areaRepository = _repositoryFactory.getAreaRepository();

      // Get or create default site for the user
      final defaultSite = await siteRepository.getOrCreateDefaultSite(user.id);
      _selectedSite = defaultSite;

      // Load all sites for the user
      _sites = await siteRepository.getSitesByOwner(user.id);

      // Load areas for the selected site
      if (_selectedSite != null) {
        _areas = await areaRepository.getAreasBySite(_selectedSite!.id);
        if (_areas.isNotEmpty) {
          _selectedArea = _areas.first;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAreasForSite(SiteModel site) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final areaRepository = _repositoryFactory.getAreaRepository();
      _areas = await areaRepository.getAreasBySite(site.id);
      _selectedArea = _areas.isNotEmpty ? _areas.first : null;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Area'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddAreaDialog(),
            tooltip: 'Add New Area',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _buildContent(),
      bottomNavigationBar: _selectedArea != null
          ? Container(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton(
                onPressed: () => _startSoloWalkabout(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Start Solo Walkabout',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading areas',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Site selection (if multiple sites exist)
          if (_sites.length > 1) ...[
            Text(
              'Site',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 8),
            Card(
              child: DropdownButtonFormField<SiteModel>(
                value: _selectedSite,
                decoration: const InputDecoration(
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  border: InputBorder.none,
                ),
                items: _sites.map((site) {
                  return DropdownMenuItem(
                    value: site,
                    child: Text(site.name),
                  );
                }).toList(),
                onChanged: (site) {
                  if (site != null) {
                    setState(() {
                      _selectedSite = site;
                    });
                    _loadAreasForSite(site);
                  }
                },
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Area selection
          Text(
            'Area',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),

          if (_areas.isEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.location_off,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No areas available',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add an area to start your walkabout',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _showAddAreaDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Area'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: _areas.length,
                itemBuilder: (context, index) {
                  final area = _areas[index];
                  final isSelected = _selectedArea?.id == area.id;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Radio<AreaModel>(
                        value: area,
                        groupValue: _selectedArea,
                        onChanged: (value) {
                          setState(() {
                            _selectedArea = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      title: Text(
                        area.name,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      subtitle: area.description != null
                          ? Text(area.description!)
                          : null,
                      trailing: PopupMenuButton(
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 20),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text('Delete',
                                    style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                        onSelected: (value) {
                          if (value == 'edit') {
                            _showEditAreaDialog(area);
                          } else if (value == 'delete') {
                            _showDeleteAreaDialog(area);
                          }
                        },
                      ),
                      onTap: () {
                        setState(() {
                          _selectedArea = area;
                        });
                      },
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  void _startSoloWalkabout() {
    if (_selectedArea == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SoloWalkaboutScreen(area: _selectedArea!),
      ),
    );
  }

  void _showAddAreaDialog() {
    if (_selectedSite == null) return;

    showDialog(
      context: context,
      builder: (context) => _AreaDialog(
        title: 'Add New Area',
        site: _selectedSite!,
        onSave: (area) async {
          try {
            final areaRepository = _repositoryFactory.getAreaRepository();
            await areaRepository.createArea(area);
            await _loadAreasForSite(_selectedSite!);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Area "${area.name}" added successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to add area: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showEditAreaDialog(AreaModel area) {
    if (_selectedSite == null) return;

    showDialog(
      context: context,
      builder: (context) => _AreaDialog(
        title: 'Edit Area',
        site: _selectedSite!,
        existingArea: area,
        onSave: (updatedArea) async {
          try {
            final areaRepository = _repositoryFactory.getAreaRepository();
            await areaRepository.updateArea(updatedArea);
            await _loadAreasForSite(_selectedSite!);
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Area "${updatedArea.name}" updated successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to update area: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showDeleteAreaDialog(AreaModel area) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Area'),
        content: Text(
            'Are you sure you want to delete "${area.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final areaRepository = _repositoryFactory.getAreaRepository();
                await areaRepository.deleteArea(area.id);

                // If this was the selected area, clear the selection
                if (_selectedArea?.id == area.id) {
                  _selectedArea = null;
                }

                await _loadAreasForSite(_selectedSite!);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Area "${area.name}" deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete area: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for adding or editing areas
class _AreaDialog extends StatefulWidget {
  final String title;
  final SiteModel site;
  final AreaModel? existingArea;
  final Function(AreaModel) onSave;

  const _AreaDialog({
    required this.title,
    required this.site,
    this.existingArea,
    required this.onSave,
  });

  @override
  State<_AreaDialog> createState() => _AreaDialogState();
}

class _AreaDialogState extends State<_AreaDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.existingArea != null) {
      _nameController.text = widget.existingArea!.name;
      _descriptionController.text = widget.existingArea!.description ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Area Name',
                hintText: 'Enter area name',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter an area name';
                }
                if (value.trim().length < 2) {
                  return 'Area name must be at least 2 characters';
                }
                return null;
              },
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                hintText: 'Enter area description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              textCapitalization: TextCapitalization.sentences,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveArea,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  Future<void> _saveArea() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final area = widget.existingArea?.copyWith(
            name: _nameController.text.trim(),
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
          ) ??
          AreaModel(
            id: const Uuid().v4(),
            name: _nameController.text.trim(),
            siteId: widget.site.id,
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
            createdAt: DateTime.now(),
          );

      widget.onSave(area);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
