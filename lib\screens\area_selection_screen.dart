import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/area_model.dart';
import '../models/site_model.dart';
import '../models/user_model.dart';
import '../providers/auth_provider.dart';
import '../repositories/repository_factory.dart';
import '../utils/app_theme.dart';
import 'solo_walkabout_screen.dart';

/// Screen for selecting area and managing areas for solo walkabout
class AreaSelectionScreen extends StatefulWidget {
  const AreaSelectionScreen({super.key});

  @override
  State<AreaSelectionScreen> createState() => _AreaSelectionScreenState();
}

class _AreaSelectionScreenState extends State<AreaSelectionScreen> {
  final RepositoryFactory _repositoryFactory = RepositoryFactory();
  
  List<SiteModel> _sites = [];
  List<AreaModel> _areas = [];
  SiteModel? _selectedSite;
  AreaModel? _selectedArea;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;
      
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final siteRepository = _repositoryFactory.getSiteRepository();
      final areaRepository = _repositoryFactory.getAreaRepository();

      // Get or create default site for the user
      final defaultSite = await siteRepository.getOrCreateDefaultSite(user.id);
      _selectedSite = defaultSite;
      
      // Load all sites for the user
      _sites = await siteRepository.getSitesByOwner(user.id);
      
      // Load areas for the selected site
      if (_selectedSite != null) {
        _areas = await areaRepository.getAreasBySite(_selectedSite!.id);
        if (_areas.isNotEmpty) {
          _selectedArea = _areas.first;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAreasForSite(SiteModel site) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final areaRepository = _repositoryFactory.getAreaRepository();
      _areas = await areaRepository.getAreasBySite(site.id);
      _selectedArea = _areas.isNotEmpty ? _areas.first : null;
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Area'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddAreaDialog(),
            tooltip: 'Add New Area',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _buildContent(),
      bottomNavigationBar: _selectedArea != null
          ? Container(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton(
                onPressed: () => _startSoloWalkabout(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Start Solo Walkabout',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading areas',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Site selection (if multiple sites exist)
          if (_sites.length > 1) ...[
            Text(
              'Site',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              child: DropdownButtonFormField<SiteModel>(
                value: _selectedSite,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  border: InputBorder.none,
                ),
                items: _sites.map((site) {
                  return DropdownMenuItem(
                    value: site,
                    child: Text(site.name),
                  );
                }).toList(),
                onChanged: (site) {
                  if (site != null) {
                    setState(() {
                      _selectedSite = site;
                    });
                    _loadAreasForSite(site);
                  }
                },
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Area selection
          Text(
            'Area',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          if (_areas.isEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Icon(
                      Icons.location_off,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No areas available',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add an area to start your walkabout',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _showAddAreaDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Area'),
                    ),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: ListView.builder(
                itemCount: _areas.length,
                itemBuilder: (context, index) {
                  final area = _areas[index];
                  final isSelected = _selectedArea?.id == area.id;
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Radio<AreaModel>(
                        value: area,
                        groupValue: _selectedArea,
                        onChanged: (value) {
                          setState(() {
                            _selectedArea = value;
                          });
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                      title: Text(
                        area.name,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      subtitle: area.description != null
                          ? Text(area.description!)
                          : null,
                      trailing: PopupMenuButton(
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 20),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text('Delete', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                        ],
                        onSelected: (value) {
                          if (value == 'edit') {
                            _showEditAreaDialog(area);
                          } else if (value == 'delete') {
                            _showDeleteAreaDialog(area);
                          }
                        },
                      ),
                      onTap: () {
                        setState(() {
                          _selectedArea = area;
                        });
                      },
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  void _startSoloWalkabout() {
    if (_selectedArea == null) return;
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SoloWalkaboutScreen(area: _selectedArea!),
      ),
    );
  }

  void _showAddAreaDialog() {
    // TODO: Implement add area dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add area functionality coming soon'),
      ),
    );
  }

  void _showEditAreaDialog(AreaModel area) {
    // TODO: Implement edit area dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit area "${area.name}" functionality coming soon'),
      ),
    );
  }

  void _showDeleteAreaDialog(AreaModel area) {
    // TODO: Implement delete area dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Delete area "${area.name}" functionality coming soon'),
      ),
    );
  }
}
