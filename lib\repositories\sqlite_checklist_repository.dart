import 'package:sqflite/sqflite.dart';
import '../models/checklist_item_model.dart';
import '../services/database_service.dart';
import 'checklist_repository.dart';

/// SQLite implementation of ChecklistRepository
class SqliteChecklistRepository implements ChecklistRepository {
  static const String _tableName = 'checklist_items';

  @override
  Future<List<ChecklistItemModel>> getChecklistBySession(String sessionId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'item_order ASC',
    );
    return maps.map((map) => ChecklistItemModel.fromMap(map)).toList();
  }

  @override
  Future<void> saveChecklist(String sessionId, List<ChecklistItemModel> items) async {
    final db = await DatabaseService.database;
    
    // Use transaction to ensure all items are saved together
    await db.transaction((txn) async {
      // First, delete existing checklist items for this session
      await txn.delete(
        _tableName,
        where: 'session_id = ?',
        whereArgs: [sessionId],
      );
      
      // Then insert all new items
      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        final itemMap = item.toMap();
        itemMap['session_id'] = sessionId;
        itemMap['item_order'] = i;
        itemMap['synced'] = 0; // Mark as not synced
        
        await txn.insert(
          _tableName,
          itemMap,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    });
  }

  @override
  Future<void> updateChecklistItem(String sessionId, ChecklistItemModel item) async {
    final db = await DatabaseService.database;
    final itemMap = item.toMap();
    itemMap['session_id'] = sessionId;
    itemMap['synced'] = 0; // Mark as not synced
    
    await db.update(
      _tableName,
      itemMap,
      where: 'session_id = ? AND id = ?',
      whereArgs: [sessionId, item.id],
    );
  }

  @override
  Future<void> deleteChecklist(String sessionId) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );
  }

  @override
  Future<List<ChecklistItemModel>> getUnsyncedChecklistItems() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => ChecklistItemModel.fromMap(map)).toList();
  }

  @override
  Future<void> markChecklistItemsAsSynced(String sessionId) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );
  }
}
