/// Enum for finding severity levels
enum FindingSeverity {
  low,
  medium,
  high,
  critical,
}

/// Enum for finding categories
enum FindingCategory {
  safety,
  environmental,
  quality,
  security,
  operational,
}

/// Enum for finding status
enum FindingStatus {
  open,
  inProgress,
  resolved,
  closed,
}

/// Finding data model
class FindingModel {
  final String id;
  final String sessionId;
  final String description;
  final FindingSeverity severity;
  final FindingCategory category;
  final String? photoPath;
  final String authorId;
  final FindingStatus status;
  final DateTime createdAt;

  const FindingModel({
    required this.id,
    required this.sessionId,
    required this.description,
    required this.severity,
    required this.category,
    this.photoPath,
    required this.authorId,
    required this.status,
    required this.createdAt,
  });

  /// Create FindingModel from map
  factory FindingModel.fromMap(Map<String, dynamic> map) {
    return FindingModel(
      id: map['id'] as String,
      sessionId: map['session_id'] as String,
      description: map['description'] as String,
      severity: FindingSeverity.values.firstWhere(
        (e) => e.name == map['severity'],
        orElse: () => FindingSeverity.low,
      ),
      category: FindingCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => FindingCategory.safety,
      ),
      photoPath: map['photo_path'] as String?,
      authorId: map['author_id'] as String,
      status: FindingStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => FindingStatus.open,
      ),
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert FindingModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'session_id': sessionId,
      'description': description,
      'severity': severity.name,
      'category': category.name,
      'photo_path': photoPath,
      'author_id': authorId,
      'status': status.name,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  FindingModel copyWith({
    String? id,
    String? sessionId,
    String? description,
    FindingSeverity? severity,
    FindingCategory? category,
    String? photoPath,
    String? authorId,
    FindingStatus? status,
    DateTime? createdAt,
  }) {
    return FindingModel(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      category: category ?? this.category,
      photoPath: photoPath ?? this.photoPath,
      authorId: authorId ?? this.authorId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FindingModel &&
        other.id == id &&
        other.sessionId == sessionId &&
        other.description == description &&
        other.severity == severity &&
        other.category == category &&
        other.photoPath == photoPath &&
        other.authorId == authorId &&
        other.status == status &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        sessionId.hashCode ^
        description.hashCode ^
        severity.hashCode ^
        category.hashCode ^
        photoPath.hashCode ^
        authorId.hashCode ^
        status.hashCode ^
        createdAt.hashCode;
  }

  @override
  String toString() {
    return 'FindingModel(id: $id, sessionId: $sessionId, description: $description, severity: $severity, category: $category, photoPath: $photoPath, authorId: $authorId, status: $status, createdAt: $createdAt)';
  }
}
