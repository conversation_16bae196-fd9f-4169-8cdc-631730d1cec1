/// Enum for user roles
enum UserRole {
  leader,
  observer,
}

/// Enum for subscription types
enum SubscriptionType {
  free,
  premium,
}

/// User data model
class UserModel {
  final String id;
  final String email;
  final String name;
  final UserRole role;
  final SubscriptionType subscription;
  final DateTime createdAt;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.subscription,
    required this.createdAt,
  });

  /// Create UserModel from map
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] as String,
      email: map['email'] as String,
      name: map['name'] as String,
      role: UserRole.values.firstWhere(
        (e) => e.name == map['role'],
        orElse: () => UserRole.observer,
      ),
      subscription: SubscriptionType.values.firstWhere(
        (e) => e.name == map['subscription'],
        orElse: () => SubscriptionType.free,
      ),
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert UserModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role.name,
      'subscription': subscription.name,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    UserRole? role,
    SubscriptionType? subscription,
    DateTime? createdAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      subscription: subscription ?? this.subscription,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel &&
        other.id == id &&
        other.email == email &&
        other.name == name &&
        other.role == role &&
        other.subscription == subscription &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        email.hashCode ^
        name.hashCode ^
        role.hashCode ^
        subscription.hashCode ^
        createdAt.hashCode;
  }

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, name: $name, role: $role, subscription: $subscription, createdAt: $createdAt)';
  }
}
