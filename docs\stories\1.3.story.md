# Story 1.3: Solo Walkabout & Area Management

## Status

Approved

## Story

**As a** Leader,
**I want** to conduct individual safety inspections and organize locations,
**so that** I can systematically document hazards and share findings with stakeholders.

## Acceptance Criteria

1. 1.3.1: Home screen offers "Solo Walkabout" option post-Leader role selection.
2. 1.3.2: Area selection dropdown lists Areas (default Site in Free tier) from SQLite/Firestore.
3. 1.3.3: Leaders can add/edit Area names (e.g., "Warehouse A" to "Storage Zone A").
4. 1.3.4: Checklist form includes 10 items (Pass/Fail, notes, 250KB photos via `image_picker`).
5. 1.3.5: Hazard form includes description, severity (Low/Medium/High), and photo.
6. 1.3.6: Findings saved in SQLite, synced to Firestore when online, with sync status UI.
7. 1.3.7: CSV export shareable via email.
8. 1.3.8: Unit and E2E tests for walkabout creation and export.

## Tasks / Subtasks

- [ ] Task 1: Create Home Screen with Solo Walkabout Option (AC: 1.3.1)
  - [ ] Design home screen UI with role-based navigation
  - [ ] Add "Solo Walkabout" button for Leader role
  - [ ] Implement navigation to area selection screen
  - [ ] Add WCAG-compliant design (large touch targets, high contrast)
  - [ ] Handle role-based UI visibility

- [ ] Task 2: Implement Area Management System (AC: 1.3.2, 1.3.3)
  - [ ] Create Area data model and repository
  - [ ] Implement SQLite schema for areas table
  - [ ] Create area selection dropdown UI component
  - [ ] Implement add/edit area functionality
  - [ ] Add default "Site" area for Free tier users
  - [ ] Implement Firestore sync for areas

- [ ] Task 3: Create Checklist Form Interface (AC: 1.3.4)
  - [ ] Design checklist UI with 10 predefined items
  - [ ] Implement Pass/Fail toggle controls
  - [ ] Add notes text input for each checklist item
  - [ ] Integrate image_picker for photo capture (250KB limit)
  - [ ] Implement photo compression and local storage
  - [ ] Add form validation and error handling

- [ ] Task 4: Implement Hazard Documentation Form (AC: 1.3.5)
  - [ ] Create hazard form UI with description field
  - [ ] Implement severity selection (Low/Medium/High)
  - [ ] Add photo capture functionality for hazards
  - [ ] Implement form validation for required fields
  - [ ] Add hazard preview and edit capabilities

- [ ] Task 5: Implement Session and Finding Data Management (AC: 1.3.6)
  - [ ] Create Session and Finding data models
  - [ ] Implement SQLite schema for sessions and findings
  - [ ] Create repository pattern for data access
  - [ ] Implement offline-first data storage
  - [ ] Add Firestore sync functionality with status tracking
  - [ ] Create sync status UI indicators
  - [ ] Handle sync conflicts and error scenarios

- [ ] Task 6: Implement CSV Export Functionality (AC: 1.3.7)
  - [ ] Create CSV generation service
  - [ ] Format findings data for export
  - [ ] Implement email sharing integration
  - [ ] Add export progress indicators
  - [ ] Handle large dataset exports

- [ ] Task 7: Testing Implementation (AC: 1.3.8)
  - [ ] Write unit tests for data models and repositories
  - [ ] Write unit tests for CSV export functionality
  - [ ] Write widget tests for all UI components
  - [ ] Write integration tests for SQLite and Firestore sync
  - [ ] Write E2E tests for complete solo walkabout workflow
  - [ ] Test offline functionality and sync scenarios

## Dev Notes

### Previous Story Insights

From Story 1.1: Firebase Authentication, Firestore, and SQLite infrastructure is established. User data model with role field exists. Repository pattern is implemented for data access abstraction.

From Story 1.2: Authentication state management with Provider pattern is implemented. Role-based UI navigation is established. WCAG compliance patterns are defined.

### Tech Stack Requirements

[Source: architecture/tech-stack.md]

- **Flutter Version:** 3.16.0 with Dart 3.2.0
- **Firebase Services:** Firestore for cloud storage, Cloud Storage for photos
- **State Management:** Provider 6.1.1 pattern for session and finding state
- **Local Storage:** SQLite via sqflite 2.3.0 for offline data caching
- **Image Processing:** image_picker 1.0.4 for photo capture and compression
- **PDF Generation:** pdf 3.10.4 for CSV export functionality

### Data Models and Schema

[Source: architecture/data-models.md, architecture/database-schema.md]

**Site Model:**
- id: String (Unique identifier)
- name: String (Site display name)
- ownerId: String (Reference to User)
- areas: List<String> (References to Area IDs)
- createdAt: DateTime

**Area Model:**
- id: String (Unique identifier)
- name: String (Area display name, editable by Leaders)
- siteId: String (Reference to parent Site)
- description: String (Optional area description)

**Session Model:**
- id: String (Unique identifier)
- type: Enum (Solo, Group) - Solo for this story
- leaderId: String (Reference to leading User)
- areaId: String (Reference to inspection Area)
- status: Enum (Active, Completed, Cancelled)
- participants: List<String> (Empty for Solo sessions)
- inviteCode: String (Not used for Solo sessions)
- createdAt: DateTime
- completedAt: DateTime?

**Finding Model:**
- id: String (Unique identifier)
- sessionId: String (Reference to parent Session)
- authorId: String (Reference to User who created)
- description: String (Hazard description)
- severity: Enum (Low, Medium, High)
- category: String (Manual category for now)
- photoUrl: String? (Reference to stored image)
- location: GeoPoint? (GPS coordinates if available)
- status: Enum (Open, InProgress, Resolved)
- assignedTo: String? (User ID for follow-up)
- createdAt: DateTime
- resolvedAt: DateTime?

**SQLite Schema Extensions:**

```sql
CREATE TABLE sites (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  owner_id TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0
);

CREATE TABLE areas (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  site_id TEXT,
  description TEXT,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (site_id) REFERENCES sites(id)
);

CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  leader_id TEXT,
  area_id TEXT,
  status TEXT,
  invite_code TEXT,
  created_at INTEGER,
  completed_at INTEGER,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (area_id) REFERENCES areas(id)
);

CREATE TABLE findings (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  description TEXT,
  severity TEXT,
  category TEXT,
  photo_path TEXT,
  author_id TEXT,
  status TEXT,
  created_at INTEGER,
  resolved_at INTEGER,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (session_id) REFERENCES sessions(id)
);
```

### Core Workflow Implementation

[Source: architecture/core-workflows.md]

**Solo Inspection Workflow:**
1. User starts Solo Walkabout from home screen
2. App creates local session in SQLite
3. User completes checklist items with Pass/Fail and notes
4. App stores findings locally in SQLite
5. User adds hazard photos (compressed to 250KB)
6. App caches compressed images locally
7. User completes inspection
8. App syncs session data to Firestore when online
9. App uploads photos to Cloud Storage
10. Firestore confirms sync completion

### File Structure and Naming

[Source: architecture/coding-standards.md]

**Files to Create:**

- `lib/screens/home_screen.dart` - Main home screen with Solo Walkabout option
- `lib/screens/area_selection_screen.dart` - Area selection and management
- `lib/screens/solo_walkabout_screen.dart` - Main inspection interface
- `lib/screens/checklist_screen.dart` - Checklist form interface
- `lib/screens/hazard_form_screen.dart` - Hazard documentation form
- `lib/models/site.dart` - Site data model
- `lib/models/area.dart` - Area data model
- `lib/models/session.dart` - Session data model
- `lib/models/finding.dart` - Finding data model
- `lib/repositories/site_repository.dart` - Site data access
- `lib/repositories/area_repository.dart` - Area data access
- `lib/repositories/session_repository.dart` - Session data access
- `lib/repositories/finding_repository.dart` - Finding data access
- `lib/services/csv_export_service.dart` - CSV generation and export
- `lib/services/photo_service.dart` - Photo capture and compression
- `lib/services/sync_service.dart` - Firestore synchronization
- `lib/providers/session_provider.dart` - Session state management
- `lib/providers/finding_provider.dart` - Finding state management
- `lib/widgets/area_dropdown.dart` - Area selection component
- `lib/widgets/checklist_item.dart` - Individual checklist item widget
- `lib/widgets/hazard_card.dart` - Hazard display component
- `lib/widgets/sync_status_indicator.dart` - Sync status UI

**Naming Conventions:**
- Classes: PascalCase (SessionRepository, SoloWalkaboutScreen)
- Variables/Functions: camelCase (createSession, currentArea)
- Files: snake_case (session_repository.dart, solo_walkabout_screen.dart)
- Constants: SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE, DEFAULT_SITE_NAME)

### Critical Implementation Rules

[Source: architecture/coding-standards.md]

1. **No direct Firebase calls in UI:** Use repository pattern for all data access
2. **Always handle offline state:** Check connectivity before network calls
3. **Validate all user inputs:** Client-side validation for forms
4. **Encrypt sensitive data:** Use secure storage for session data
5. **Log errors appropriately:** Include context but protect privacy
6. **Photo compression:** Limit photos to 250KB using image compression
7. **Sync status tracking:** Always show user sync status for offline data

### Component Architecture

[Source: architecture/components.md]

**Mobile Application Modules:**
- Inspection Module: Checklist and hazard documentation
- Offline Storage: SQLite repository layer for sessions and findings
- Sync Engine: Firestore synchronization with conflict resolution
- Photo Management: Image capture, compression, and storage

**Security Rules:**
- Role-based access control: Only Leaders can create Solo sessions
- Data isolation: Users can only access their own sessions and findings
- Read/write permissions: Based on user roles and ownership

### Testing

[Source: architecture/test-strategy.md]

**Test Types Required:**

- **Unit Tests:** Data models, repositories, CSV export service, photo service
- **Widget Tests:** Home screen, area selection, checklist form, hazard form
- **Integration Tests:** SQLite operations, Firestore sync, photo capture
- **E2E Tests:** Complete solo walkabout workflow from start to CSV export

**Test Coverage:**
- Minimum 80% code coverage for business logic
- Test offline functionality and sync scenarios
- Test photo capture and compression
- Test CSV export with various data sizes
- Test role-based access (Leader vs Observer)

**Test Data:**
- Use isolated test Firebase project
- Mock external dependencies for unit tests
- Test on minimum supported devices (iPhone 7, Android 8.0)
- Automated test data cleanup after runs

**Specific Test Scenarios:**
- Solo walkabout creation and completion
- Area management (add/edit/delete)
- Checklist completion with photos
- Hazard documentation with severity levels
- Offline data storage and online sync
- CSV export and email sharing
- Role-based UI visibility and functionality

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2024-01-XX | 1.0 | Initial story creation | Scrum Master |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used

*To be filled by dev agent*

### Debug Log References

*To be filled by dev agent*

### Completion Notes List

*To be filled by dev agent*

### File List

*To be filled by dev agent*

## QA Results

*Results from QA Agent review of the completed story implementation*