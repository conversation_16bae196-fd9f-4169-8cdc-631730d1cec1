import '../models/checklist_item_model.dart';

/// Abstract repository interface for checklist data operations
abstract class ChecklistRepository {
  /// Get checklist items by session ID
  Future<List<ChecklistItemModel>> getChecklistBySession(String sessionId);

  /// Save checklist items for a session
  Future<void> saveChecklist(String sessionId, List<ChecklistItemModel> items);

  /// Update a single checklist item
  Future<void> updateChecklistItem(String sessionId, ChecklistItemModel item);

  /// Delete checklist for a session
  Future<void> deleteChecklist(String sessionId);

  /// Get checklist items that haven't been synced to remote storage
  Future<List<ChecklistItemModel>> getUnsyncedChecklistItems();

  /// Mark checklist items as synced
  Future<void> markChecklistItemsAsSynced(String sessionId);
}
