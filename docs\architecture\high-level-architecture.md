# High Level Architecture

### Technical Summary

SafeStride employs a **serverless mobile-first architecture** with Flutter providing cross-platform mobile capabilities and Google Firebase delivering backend services. The system prioritizes offline-first functionality through SQLite local caching with Firebase Firestore synchronization. Core architectural patterns include Repository pattern for data access, Provider pattern for state management, and Event-driven communication for real-time collaboration features.

### High Level Overview

**Architectural Style:** Serverless with offline-first mobile client
**Repository Structure:** Monorepo (Flutter single codebase for iOS/Android)
**Service Architecture:** Google Firebase serverless backend

**Primary User Flow:**
1. User authenticates via Firebase Auth (email/SSO)
2. Role selection (Leader/Observer) determines available features
3. Offline-capable safety inspections with SQLite storage
4. Background sync to Firestore when connectivity available
5. Real-time collaboration through Firestore listeners
6. AI-powered hazard detection using on-device TensorFlow Lite

### Architectural Patterns

- **Serverless Architecture:** Firebase Functions + Firestore - *Rationale:* Eliminates infrastructure management, automatic scaling, cost-effective for variable workloads
- **Offline-First Pattern:** SQLite + Firestore sync - *Rationale:* Critical for industrial environments with poor connectivity
- **Repository Pattern:** Abstract data access layer - *Rationale:* Enables testing, supports offline/online data source switching
- **Provider Pattern:** Flutter state management - *Rationale:* Reactive UI updates, clean separation of business logic
- **Event-Driven Communication:** Firestore real-time listeners - *Rationale:* Enables collaborative features without polling

