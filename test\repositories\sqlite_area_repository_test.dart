import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:safestride/models/area_model.dart';
import 'package:safestride/repositories/sqlite_area_repository.dart';
import 'package:safestride/services/database_service.dart';

void main() {
  group('SqliteAreaRepository', () {
    late SqliteAreaRepository repository;
    late Database database;

    setUpAll(() {
      // Initialize FFI
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    });

    setUp(() async {
      // Create in-memory database for testing
      database = await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: (db, version) async {
          await db.execute('''
            CREATE TABLE areas (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              site_id TEXT,
              description TEXT,
              qr_code TEXT,
              created_at INTEGER,
              synced INTEGER DEFAULT 0
            )
          ''');
        },
      );

      // Override the database service for testing
      DatabaseService.setTestDatabase(database);
      repository = SqliteAreaRepository();
    });

    tearDown(() async {
      await database.close();
    });

    test('should create area successfully', () async {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        description: 'Test description',
        qrCode: 'QR123',
        createdAt: DateTime(2024, 1, 1),
      );

      await repository.createArea(area);

      final retrievedArea = await repository.getAreaById('test-id');
      expect(retrievedArea, isNotNull);
      expect(retrievedArea!.id, 'test-id');
      expect(retrievedArea.name, 'Test Area');
      expect(retrievedArea.siteId, 'site-id');
      expect(retrievedArea.description, 'Test description');
      expect(retrievedArea.qrCode, 'QR123');
    });

    test('should return null for non-existent area', () async {
      final area = await repository.getAreaById('non-existent');
      expect(area, isNull);
    });

    test('should update area successfully', () async {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      await repository.createArea(area);

      final updatedArea = area.copyWith(
        name: 'Updated Area',
        description: 'Updated description',
      );

      await repository.updateArea(updatedArea);

      final retrievedArea = await repository.getAreaById('test-id');
      expect(retrievedArea!.name, 'Updated Area');
      expect(retrievedArea.description, 'Updated description');
    });

    test('should delete area successfully', () async {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      await repository.createArea(area);
      expect(await repository.getAreaById('test-id'), isNotNull);

      await repository.deleteArea('test-id');
      expect(await repository.getAreaById('test-id'), isNull);
    });

    test('should get areas by site', () async {
      final area1 = AreaModel(
        id: 'area-1',
        name: 'Area 1',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'area-2',
        name: 'Area 2',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 2),
      );

      final area3 = AreaModel(
        id: 'area-3',
        name: 'Area 3',
        siteId: 'site-2',
        createdAt: DateTime(2024, 1, 3),
      );

      await repository.createArea(area1);
      await repository.createArea(area2);
      await repository.createArea(area3);

      final site1Areas = await repository.getAreasBySite('site-1');
      expect(site1Areas.length, 2);
      expect(site1Areas.map((a) => a.id), containsAll(['area-1', 'area-2']));

      final site2Areas = await repository.getAreasBySite('site-2');
      expect(site2Areas.length, 1);
      expect(site2Areas.first.id, 'area-3');
    });

    test('should get all areas', () async {
      final area1 = AreaModel(
        id: 'area-1',
        name: 'Area 1',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'area-2',
        name: 'Area 2',
        siteId: 'site-2',
        createdAt: DateTime(2024, 1, 2),
      );

      await repository.createArea(area1);
      await repository.createArea(area2);

      final allAreas = await repository.getAllAreas();
      expect(allAreas.length, 2);
      expect(allAreas.map((a) => a.id), containsAll(['area-1', 'area-2']));
    });

    test('should get unsynced areas', () async {
      final area1 = AreaModel(
        id: 'area-1',
        name: 'Area 1',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'area-2',
        name: 'Area 2',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 2),
      );

      await repository.createArea(area1);
      await repository.createArea(area2);

      // Mark one as synced
      await repository.markAreaAsSynced('area-1');

      final unsyncedAreas = await repository.getUnsyncedAreas();
      expect(unsyncedAreas.length, 1);
      expect(unsyncedAreas.first.id, 'area-2');
    });

    test('should mark area as synced', () async {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      await repository.createArea(area);

      final unsyncedBefore = await repository.getUnsyncedAreas();
      expect(unsyncedBefore.length, 1);

      await repository.markAreaAsSynced('test-id');

      final unsyncedAfter = await repository.getUnsyncedAreas();
      expect(unsyncedAfter.length, 0);
    });

    test('should get areas count by site', () async {
      final area1 = AreaModel(
        id: 'area-1',
        name: 'Area 1',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'area-2',
        name: 'Area 2',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 2),
      );

      final area3 = AreaModel(
        id: 'area-3',
        name: 'Area 3',
        siteId: 'site-2',
        createdAt: DateTime(2024, 1, 3),
      );

      await repository.createArea(area1);
      await repository.createArea(area2);
      await repository.createArea(area3);

      final site1Count = await repository.getAreasCountBySite('site-1');
      expect(site1Count, 2);

      final site2Count = await repository.getAreasCountBySite('site-2');
      expect(site2Count, 1);

      final nonExistentSiteCount = await repository.getAreasCountBySite('non-existent');
      expect(nonExistentSiteCount, 0);
    });

    test('should search areas by name', () async {
      final area1 = AreaModel(
        id: 'area-1',
        name: 'Production Area',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'area-2',
        name: 'Storage Area',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 2),
      );

      final area3 = AreaModel(
        id: 'area-3',
        name: 'Office Space',
        siteId: 'site-1',
        createdAt: DateTime(2024, 1, 3),
      );

      await repository.createArea(area1);
      await repository.createArea(area2);
      await repository.createArea(area3);

      final areaResults = await repository.searchAreasByName('Area');
      expect(areaResults.length, 2);
      expect(areaResults.map((a) => a.id), containsAll(['area-1', 'area-2']));

      final productionResults = await repository.searchAreasByName('Production');
      expect(productionResults.length, 1);
      expect(productionResults.first.id, 'area-1');

      final noResults = await repository.searchAreasByName('Nonexistent');
      expect(noResults.length, 0);
    });
  });
}
