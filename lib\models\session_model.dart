/// Enum for session types
enum SessionType {
  inspection,
  audit,
  walkthrough,
}

/// Enum for session status
enum SessionStatus {
  active,
  completed,
  cancelled,
}

/// Session data model
class SessionModel {
  final String id;
  final SessionType type;
  final String leaderId;
  final String areaId;
  final SessionStatus status;
  final String inviteCode;
  final DateTime createdAt;
  final DateTime? completedAt;

  const SessionModel({
    required this.id,
    required this.type,
    required this.leaderId,
    required this.areaId,
    required this.status,
    required this.inviteCode,
    required this.createdAt,
    this.completedAt,
  });

  /// Create SessionModel from map
  factory SessionModel.fromMap(Map<String, dynamic> map) {
    return SessionModel(
      id: map['id'] as String,
      type: SessionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => SessionType.inspection,
      ),
      leaderId: map['leader_id'] as String,
      areaId: map['area_id'] as String,
      status: SessionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => SessionStatus.active,
      ),
      inviteCode: map['invite_code'] as String,
      createdAt: map['created_at'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int)
          : DateTime.parse(map['created_at'] as String),
      completedAt: map['completed_at'] != null
          ? (map['completed_at'] is int
              ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'] as int)
              : DateTime.parse(map['completed_at'] as String))
          : null,
    );
  }

  /// Convert SessionModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.name,
      'leader_id': leaderId,
      'area_id': areaId,
      'status': status.name,
      'invite_code': inviteCode,
      'created_at': createdAt.millisecondsSinceEpoch,
      'completed_at': completedAt?.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  SessionModel copyWith({
    String? id,
    SessionType? type,
    String? leaderId,
    String? areaId,
    SessionStatus? status,
    String? inviteCode,
    DateTime? createdAt,
    DateTime? completedAt,
  }) {
    return SessionModel(
      id: id ?? this.id,
      type: type ?? this.type,
      leaderId: leaderId ?? this.leaderId,
      areaId: areaId ?? this.areaId,
      status: status ?? this.status,
      inviteCode: inviteCode ?? this.inviteCode,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SessionModel &&
        other.id == id &&
        other.type == type &&
        other.leaderId == leaderId &&
        other.areaId == areaId &&
        other.status == status &&
        other.inviteCode == inviteCode &&
        other.createdAt == createdAt &&
        other.completedAt == completedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        type.hashCode ^
        leaderId.hashCode ^
        areaId.hashCode ^
        status.hashCode ^
        inviteCode.hashCode ^
        createdAt.hashCode ^
        completedAt.hashCode;
  }

  @override
  String toString() {
    return 'SessionModel(id: $id, type: $type, leaderId: $leaderId, areaId: $areaId, status: $status, inviteCode: $inviteCode, createdAt: $createdAt, completedAt: $completedAt)';
  }
}
