import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/user_model.dart';
import '../repositories/user_repository.dart';
import 'firebase_service.dart';

/// Service class for handling authentication operations
class AuthService extends ChangeNotifier {
  final UserRepository _userRepository;
  final GoogleSignIn _googleSignIn = GoogleSignIn.instance;

  User? _currentUser;
  bool _isLoading = false;
  bool _isOffline = false;

  AuthService(this._userRepository) {
    _initializeAuthListener();
    _checkConnectivity();
  }

  /// Current authenticated user
  User? get currentUser => _currentUser;

  /// Loading state
  bool get isLoading => _isLoading;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Check if device is offline
  bool get isOffline => _isOffline;

  /// Initialize auth state listener
  void _initializeAuthListener() {
    FirebaseService.auth.authStateChanges().listen((User? user) {
      _currentUser = user;
      notifyListeners();
    });
  }

  /// Check connectivity status
  void _checkConnectivity() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      _isOffline = result == ConnectivityResult.none;
      notifyListeners();
    });
  }

  /// Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);

      final credential = await FirebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        final userModel =
            await _userRepository.getUserById(credential.user!.uid);
        if (userModel != null) {
          // Cache credentials for offline access
          await _userRepository.cacheUserCredentials(email, password);
        }
        return userModel;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up with email and password
  Future<UserModel?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      _setLoading(true);

      final credential =
          await FirebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);

        // Create user model
        final userModel = UserModel(
          id: credential.user!.uid,
          email: email,
          name: name,
          role: UserRole.observer,
          subscription: SubscriptionType.free,
          createdAt: DateTime.now(),
        );

        // Save user to repository
        await _userRepository.createUser(userModel);

        // Cache credentials for offline access
        await _userRepository.cacheUserCredentials(email, password);

        return userModel;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await FirebaseService.auth.signOut();
      await _userRepository.clearCachedCredentials();
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await FirebaseService.auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Try offline authentication
  Future<UserModel?> tryOfflineAuthentication({
    required String email,
    required String password,
  }) async {
    return await _userRepository.authenticateOffline(email, password);
  }

  /// Sign in with Google
  Future<UserModel?> signInWithGoogle() async {
    try {
      _setLoading(true);

      // Check if offline
      if (_isOffline) {
        throw 'Google Sign-In requires internet connection';
      }

      // Use the new Google Sign-In API
      final GoogleSignInAccount googleUser;
      if (_googleSignIn.supportsAuthenticate()) {
        googleUser = await _googleSignIn.authenticate();
      } else {
        throw 'Google Sign-In authentication not supported on this platform';
      }

      // Get authentication tokens
      final authentication = googleUser.authentication;

      // Get authorization for Firebase scopes
      const List<String> scopes = <String>[
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile',
      ];

      final authorization =
          await googleUser.authorizationClient.authorizationForScopes(scopes);
      if (authorization == null) {
        throw 'Failed to get Google authorization';
      }

      // Create Firebase credential
      final credential = GoogleAuthProvider.credential(
        accessToken: authorization.accessToken,
        idToken: authentication.idToken,
      );

      final userCredential =
          await FirebaseService.auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        // Check if user exists in our database
        UserModel? userModel =
            await _userRepository.getUserById(userCredential.user!.uid);

        if (userModel == null) {
          // Create new user
          userModel = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email ?? '',
            name: userCredential.user!.displayName ?? '',
            role: UserRole.observer,
            subscription: SubscriptionType.free,
            createdAt: DateTime.now(),
          );
          await _userRepository.createUser(userModel);
        }

        return userModel;
      }
      return null;
    } catch (e) {
      throw 'Google Sign-In failed: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Incorrect password.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak.';
      case 'invalid-email':
        return 'Invalid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      default:
        return 'Authentication failed: ${e.message}';
    }
  }
}
