# SafeStride API Specification

**Scope:** Client ↔ Firebase interactions & helper Cloud Functions supporting practical/essential solutions.

---

## 1. Sync API (Firestore Collections)
| Collection | Document ID | Description |
|------------|-------------|-------------|
| `sessions` | `<sessionId>` | Walkabout session metadata (area, leaderId, status) |
| `sessions/{sessionId}/findings` | `<findingId>` | Individual hazard/checklist entries |
| `users` | `<uid>` | User profile, role, subscription info |
| `ai_models` | `<name>` | Metadata for on-device models (see 2) |

### 1.1 Field Conventions
- All docs include `created_at`, `updated_at`, `deleted` flags.
- Timestamps stored as Firestore `Timestamp` (UTC).

### 1.2 Sync Queue Schema (SQLite)
```sql
CREATE TABLE sync_queue (
  id TEXT PRIMARY KEY,
  table_name TEXT NOT NULL,
  row_id TEXT NOT NULL,
  action TEXT CHECK(action IN ('insert','update','delete')),
  payload TEXT NOT NULL,
  ts INTEGER NOT NULL,
  retries INTEGER DEFAULT 0
);
```

---

## 2. AI Model Metadata
`ai_models/{modelName}`
```json
{
  "latest_version": "1.0.3",
  "url": "https://firebasestorage.googleapis.com/…/mobilenet_v2_v1.0.3.tflite",
  "checksum": "sha256-abc123…",
  "size": 3892736
}
```
Client fetches this document, compares version, downloads model if newer.

---

## 3. Cloud Functions (Optional)
| Function | Trigger | Purpose |
|----------|---------|---------|
| `archiveOldSessions` | Scheduled daily | Move sessions >3y to Coldline Storage, delete thumbnails |
| `purgeUserData` | Callable / Auth delete | Remove all user data to satisfy GDPR delete |
| `exportBigQuery` | Scheduled hourly | Stream Firestore updates to BigQuery |

---

## 4. Security Rules Snippet
```rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /sessions/{sessionId} {
      allow read: if isObserverOrLeader(sessionId);
      allow write: if isLeader(sessionId);
      match /findings/{findingId} {
        allow read, write: if request.auth.uid == resource.data.authorId;
      }
    }
    match /ai_models/{id} {
      allow read: if true;
    }
  }
}
```

---

*This specification is concise and focused on endpoints required for the essential solutions.*
