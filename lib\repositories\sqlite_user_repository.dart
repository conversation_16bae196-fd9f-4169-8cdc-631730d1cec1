import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:sqflite/sqflite.dart';
import '../models/user_model.dart';
import '../services/database_service.dart';
import 'user_repository.dart';

/// SQLite implementation of UserRepository
class SqliteUserRepository implements UserRepository {
  static const String _tableName = 'users';
  static const String _credentialsTable = 'cached_credentials';

  @override
  Future<UserModel?> getUserById(String id) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return UserModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<UserModel?> getUserByEmail(String email) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return UserModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<void> createUser(UserModel user) async {
    final db = await DatabaseService.database;
    await db.insert(
      _tableName,
      user.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> updateUser(UserModel user) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  @override
  Future<void> deleteUser(String id) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    final db = await DatabaseService.database;
    final maps = await db.query(_tableName);
    return maps.map((map) => UserModel.fromMap(map)).toList();
  }

  @override
  Future<void> cacheUserCredentials(String email, String password) async {
    final db = await DatabaseService.database;
    
    // Generate salt and hash password
    final salt = _generateSalt();
    final passwordHash = _hashPassword(password, salt);
    
    await db.insert(
      _credentialsTable,
      {
        'email': email,
        'password_hash': passwordHash,
        'salt': salt,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<UserModel?> authenticateOffline(String email, String password) async {
    final db = await DatabaseService.database;
    
    // Get cached credentials
    final credentialMaps = await db.query(
      _credentialsTable,
      where: 'email = ?',
      whereArgs: [email],
    );
    
    if (credentialMaps.isEmpty) {
      return null;
    }
    
    final credentials = credentialMaps.first;
    final storedHash = credentials['password_hash'] as String;
    final salt = credentials['salt'] as String;
    
    // Verify password
    final inputHash = _hashPassword(password, salt);
    if (inputHash != storedHash) {
      return null;
    }
    
    // Get user data
    return await getUserByEmail(email);
  }

  @override
  Future<void> clearCachedCredentials() async {
    final db = await DatabaseService.database;
    await db.delete(_credentialsTable);
  }

  @override
  Future<List<UserModel>> getUnsyncedUsers() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => UserModel.fromMap(map)).toList();
  }

  @override
  Future<void> markUserAsSynced(String id) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// Generate random salt for password hashing
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// Hash password with salt using SHA-256
  String _hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
