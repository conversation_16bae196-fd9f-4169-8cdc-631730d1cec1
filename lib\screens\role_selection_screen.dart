import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../repositories/user_repository.dart';
import '../widgets/role_card.dart';

/// Role selection screen for choosing Leader or Observer role
class RoleSelectionScreen extends StatefulWidget {
  final UserModel user;

  const RoleSelectionScreen({
    super.key,
    required this.user,
  });

  @override
  State<RoleSelectionScreen> createState() => _RoleSelectionScreenState();
}

class _RoleSelectionScreenState extends State<RoleSelectionScreen> {
  UserRole? _selectedRole;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedRole = widget.user.role;
  }

  Future<void> _confirmRoleSelection() async {
    if (_selectedRole == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Update user role in repository
      final userRepository =
          Provider.of<UserRepository>(context, listen: false);
      final updatedUser = widget.user.copyWith(role: _selectedRole);
      await userRepository.updateUser(updatedUser);

      if (mounted) {
        // Check if this is a first-time user for premium trial
        if (widget.user.subscription == SubscriptionType.free) {
          _showPremiumTrialDialog();
        } else {
          _navigateToHome();
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Role Update Failed', e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showPremiumTrialDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Welcome to SafeStride!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Start your 30-day Premium trial and unlock:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            const Text('• Unlimited safety inspections'),
            const Text('• Advanced reporting features'),
            const Text('• Team collaboration tools'),
            const Text('• Priority support'),
            const SizedBox(height: 16),
            Text(
              'No payment required. Cancel anytime.',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToHome();
            },
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startPremiumTrial();
            },
            child: const Text('Start Free Trial'),
          ),
        ],
      ),
    );
  }

  Future<void> _startPremiumTrial() async {
    try {
      final userRepository =
          Provider.of<UserRepository>(context, listen: false);
      final updatedUser = widget.user.copyWith(
        role: _selectedRole,
        subscription: SubscriptionType.premium,
      );
      await userRepository.updateUser(updatedUser);

      if (mounted) {
        _navigateToHome();
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Trial Activation Failed', e.toString());
      }
    }
  }

  void _navigateToHome() {
    // TODO: Navigate to home screen
    // For now, just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Welcome, ${widget.user.name}! Role: ${_selectedRole?.name}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Select Your Role'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () async {
              final authService =
                  Provider.of<AuthService>(context, listen: false);
              final navigator = Navigator.of(context);
              await authService.signOut();
              if (mounted) {
                navigator.pushReplacementNamed('/login');
              }
            },
            icon: const Icon(Icons.logout),
            tooltip: 'Sign Out',
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Welcome Message
              Text(
                'Welcome, ${widget.user.name}!',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Choose your role to get started with SafeStride',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 48),

              // Role Cards
              Expanded(
                child: Column(
                  children: [
                    RoleCard(
                      role: UserRole.leader,
                      isSelected: _selectedRole == UserRole.leader,
                      onTap: () {
                        setState(() {
                          _selectedRole = UserRole.leader;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    RoleCard(
                      role: UserRole.observer,
                      isSelected: _selectedRole == UserRole.observer,
                      onTap: () {
                        setState(() {
                          _selectedRole = UserRole.observer;
                        });
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Continue Button
              ElevatedButton(
                onPressed: _selectedRole != null && !_isLoading
                    ? _confirmRoleSelection
                    : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  minimumSize: const Size(double.infinity, 56),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(
                        'Continue',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),

              const SizedBox(height: 16),

              // Role Change Note
              Text(
                'You can change your role later in settings',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
