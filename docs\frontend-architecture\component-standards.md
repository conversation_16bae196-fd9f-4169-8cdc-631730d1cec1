# Component Standards

## Widget Template
```dart
class SafeStrideButton extends StatelessWidget {
  const SafeStrideButton({
    Key? key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.variant = ButtonVariant.primary,
  }) : super(key: key);

  final VoidCallback? onPressed;
  final String text;
  final bool isLoading;
  final ButtonVariant variant;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: _getButtonStyle(context, variant),
      child: isLoading 
        ? const CircularProgressIndicator()
        : Text(text),
    );
  }
}
```

## Naming Conventions
- **Widgets:** PascalCase with descriptive names (InspectionCard, HazardForm)
- **Files:** snake_case matching widget names (inspection_card.dart)
- **Private methods:** _camelCase with underscore prefix
- **Constants:** SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE)
