import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import '../models/site_model.dart';
import '../models/area_model.dart';
import '../services/database_service.dart';
import 'site_repository.dart';
import 'sqlite_area_repository.dart';

/// SQLite implementation of SiteRepository
class SqliteSiteRepository implements SiteRepository {
  static const String _tableName = 'sites';
  static const String _defaultSiteName = 'Site';
  final SqliteAreaRepository _areaRepository = SqliteAreaRepository();

  @override
  Future<SiteModel?> getSiteById(String id) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SiteModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<void> createSite(SiteModel site) async {
    final db = await DatabaseService.database;
    await db.insert(
      _tableName,
      site.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> updateSite(SiteModel site) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      site.toMap(),
      where: 'id = ?',
      whereArgs: [site.id],
    );
  }

  @override
  Future<void> deleteSite(String id) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<SiteModel>> getSitesByOwner(String ownerId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'owner_id = ?',
      whereArgs: [ownerId],
      orderBy: 'name ASC',
    );
    return maps.map((map) => SiteModel.fromMap(map)).toList();
  }

  @override
  Future<List<SiteModel>> getAllSites() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      orderBy: 'name ASC',
    );
    return maps.map((map) => SiteModel.fromMap(map)).toList();
  }

  @override
  Future<List<SiteModel>> getUnsyncedSites() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => SiteModel.fromMap(map)).toList();
  }

  @override
  Future<void> markSiteAsSynced(String id) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<SiteModel>> searchSitesByName(String query) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'name LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => SiteModel.fromMap(map)).toList();
  }

  @override
  Future<SiteModel> getOrCreateDefaultSite(String ownerId) async {
    // First, try to get existing sites for the owner
    final existingSites = await getSitesByOwner(ownerId);

    if (existingSites.isNotEmpty) {
      return existingSites.first;
    }

    // Create default site if none exists
    final defaultSite = SiteModel(
      id: const Uuid().v4(),
      name: _defaultSiteName,
      address: 'Default Location',
      ownerId: ownerId,
      createdAt: DateTime.now(),
    );

    await createSite(defaultSite);

    // Create default area for the site
    final defaultArea = AreaModel(
      id: const Uuid().v4(),
      name: _defaultSiteName,
      siteId: defaultSite.id,
      description: 'Default area for inspections',
      createdAt: DateTime.now(),
    );

    await _areaRepository.createArea(defaultArea);

    return defaultSite;
  }
}
