import '../models/user_model.dart';
import '../services/firestore_service.dart';
import 'user_repository.dart';

/// Firebase implementation of UserRepository
class FirebaseUserRepository implements UserRepository {
  @override
  Future<UserModel?> getUserById(String id) async {
    return await FirestoreService.getUser(id);
  }

  @override
  Future<UserModel?> getUserByEmail(String email) async {
    // Note: This would require a Firestore query by email
    // For now, we'll throw an exception as this should be handled by auth
    throw UnimplementedError('Use Firebase Auth for email-based user lookup');
  }

  @override
  Future<void> createUser(UserModel user) async {
    await FirestoreService.createUser(user);
  }

  @override
  Future<void> updateUser(UserModel user) async {
    await FirestoreService.updateUser(user);
  }

  @override
  Future<void> deleteUser(String id) async {
    // Note: User deletion should be handled carefully in production
    throw UnimplementedError('User deletion not implemented for security reasons');
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    // Note: This would require admin privileges in production
    throw UnimplementedError('Getting all users requires admin privileges');
  }

  @override
  Future<void> cacheUserCredentials(String email, String password) async {
    // Firebase handles authentication, no need to cache credentials
    // This is handled by the local repository
    throw UnimplementedError('Credential caching is handled by local repository');
  }

  @override
  Future<UserModel?> authenticateOffline(String email, String password) async {
    // Offline authentication is handled by local repository
    throw UnimplementedError('Offline authentication is handled by local repository');
  }

  @override
  Future<void> clearCachedCredentials() async {
    // Credential clearing is handled by local repository
    throw UnimplementedError('Credential clearing is handled by local repository');
  }

  @override
  Future<List<UserModel>> getUnsyncedUsers() async {
    // Firebase is the source of truth, no unsynced users
    return [];
  }

  @override
  Future<void> markUserAsSynced(String id) async {
    // No-op for Firebase repository
  }
}
