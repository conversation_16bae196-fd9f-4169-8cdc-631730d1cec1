import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/repositories/repository_factory.dart';
import 'package:safestride/repositories/user_repository.dart';
import 'package:safestride/repositories/session_repository.dart';
import 'package:safestride/repositories/finding_repository.dart';
import 'package:safestride/repositories/sqlite_user_repository.dart';
import 'package:safestride/repositories/sqlite_session_repository.dart';
import 'package:safestride/repositories/sqlite_finding_repository.dart';
import 'package:safestride/repositories/firebase_user_repository.dart';

void main() {
  group('RepositoryFactory', () {
    late RepositoryFactory factory;

    setUp(() {
      factory = RepositoryFactory();
    });

    test('should be a singleton', () {
      final factory1 = RepositoryFactory();
      final factory2 = RepositoryFactory();
      
      expect(factory1, same(factory2));
    });

    test('should return session repository', () {
      final sessionRepository = factory.getSessionRepository();
      
      expect(sessionRepository, isA<SessionRepository>());
      expect(sessionRepository, isA<SqliteSessionRepository>());
    });

    test('should return finding repository', () {
      final findingRepository = factory.getFindingRepository();
      
      expect(findingRepository, isA<FindingRepository>());
      expect(findingRepository, isA<SqliteFindingRepository>());
    });

    test('should return local user repository', () {
      final localUserRepository = factory.getLocalUserRepository();
      
      expect(localUserRepository, isA<UserRepository>());
      expect(localUserRepository, isA<SqliteUserRepository>());
    });

    test('should return remote user repository', () {
      final remoteUserRepository = factory.getRemoteUserRepository();
      
      expect(remoteUserRepository, isA<UserRepository>());
      expect(remoteUserRepository, isA<FirebaseUserRepository>());
    });

    test('should create hybrid user repository', () {
      final hybridRepository = factory.createHybridUserRepository();
      
      expect(hybridRepository, isA<HybridUserRepository>());
      expect(hybridRepository, isA<UserRepository>());
    });

    test('should return consistent repository instances', () {
      final sessionRepo1 = factory.getSessionRepository();
      final sessionRepo2 = factory.getSessionRepository();
      
      expect(sessionRepo1, same(sessionRepo2));
      
      final findingRepo1 = factory.getFindingRepository();
      final findingRepo2 = factory.getFindingRepository();
      
      expect(findingRepo1, same(findingRepo2));
    });
  });

  group('HybridUserRepository', () {
    late RepositoryFactory factory;
    late HybridUserRepository hybridRepository;

    setUp(() {
      factory = RepositoryFactory();
      hybridRepository = factory.createHybridUserRepository();
    });

    test('should be created successfully', () {
      expect(hybridRepository, isNotNull);
      expect(hybridRepository, isA<UserRepository>());
    });

    test('should implement all UserRepository methods', () {
      // Test that all methods exist and return the correct types
      expect(() => hybridRepository.getUserById('test-id'), returnsNormally);
      expect(() => hybridRepository.getUserByEmail('<EMAIL>'), returnsNormally);
      expect(() => hybridRepository.getAllUsers(), returnsNormally);
      expect(() => hybridRepository.getUnsyncedUsers(), returnsNormally);
      expect(() => hybridRepository.cacheUserCredentials('email', 'password'), returnsNormally);
      expect(() => hybridRepository.authenticateOffline('email', 'password'), returnsNormally);
      expect(() => hybridRepository.clearCachedCredentials(), returnsNormally);
    });
  });
}
