import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/area_model.dart';
import 'package:safestride/models/site_model.dart';
import 'package:safestride/models/session_model.dart';
import 'package:safestride/models/finding_model.dart';
import 'package:safestride/models/checklist_item_model.dart';
import 'package:safestride/services/csv_export_service.dart';
import 'package:safestride/services/photo_service.dart';

void main() {
  group('Core Functionality Tests', () {
    test('Area model should work correctly', () {
      final area = AreaModel(
        id: 'test-area',
        name: 'Test Area',
        siteId: 'test-site',
        description: 'Test description',
        createdAt: DateTime.now(),
      );

      expect(area.id, 'test-area');
      expect(area.name, 'Test Area');
      expect(area.siteId, 'test-site');
      expect(area.description, 'Test description');

      final map = area.toMap();
      final fromMap = AreaModel.fromMap(map);
      expect(fromMap.id, area.id);
      expect(fromMap.name, area.name);
    });

    test('Site model should work correctly', () {
      final site = SiteModel(
        id: 'test-site',
        name: 'Test Site',
        address: 'Test Address',
        ownerId: 'test-owner',
        createdAt: DateTime.now(),
      );

      expect(site.id, 'test-site');
      expect(site.name, 'Test Site');
      expect(site.address, 'Test Address');
      expect(site.ownerId, 'test-owner');

      final map = site.toMap();
      final fromMap = SiteModel.fromMap(map);
      expect(fromMap.id, site.id);
      expect(fromMap.name, site.name);
    });

    test('Session model should work correctly', () {
      final session = SessionModel(
        id: 'test-session',
        type: SessionType.inspection,
        leaderId: 'test-leader',
        areaId: 'test-area',
        status: SessionStatus.active,
        inviteCode: 'TEST123',
        createdAt: DateTime.now(),
      );

      expect(session.id, 'test-session');
      expect(session.type, SessionType.inspection);
      expect(session.leaderId, 'test-leader');
      expect(session.areaId, 'test-area');
      expect(session.status, SessionStatus.active);

      final map = session.toMap();
      final fromMap = SessionModel.fromMap(map);
      expect(fromMap.id, session.id);
      expect(fromMap.type, session.type);
    });

    test('Finding model should work correctly', () {
      final finding = FindingModel(
        id: 'test-finding',
        sessionId: 'test-session',
        description: 'Test hazard',
        severity: FindingSeverity.medium,
        category: FindingCategory.safety,
        authorId: 'test-author',
        status: FindingStatus.open,
        createdAt: DateTime.now(),
      );

      expect(finding.id, 'test-finding');
      expect(finding.sessionId, 'test-session');
      expect(finding.description, 'Test hazard');
      expect(finding.severity, FindingSeverity.medium);
      expect(finding.category, FindingCategory.safety);

      final map = finding.toMap();
      final fromMap = FindingModel.fromMap(map);
      expect(fromMap.id, finding.id);
      expect(fromMap.severity, finding.severity);
    });

    test('Checklist item model should work correctly', () {
      final item = ChecklistItemModel(
        id: 'test-item',
        title: 'Test Item',
        description: 'Test description',
        status: ChecklistItemStatus.pass,
        notes: 'Test notes',
      );

      expect(item.id, 'test-item');
      expect(item.title, 'Test Item');
      expect(item.description, 'Test description');
      expect(item.status, ChecklistItemStatus.pass);
      expect(item.notes, 'Test notes');
      expect(item.isCompleted, true);
      expect(item.isPassed, true);
      expect(item.isFailed, false);

      final map = item.toMap();
      final fromMap = ChecklistItemModel.fromMap(map);
      expect(fromMap.id, item.id);
      expect(fromMap.status, item.status);
    });

    test('Default checklist items should be generated correctly', () {
      final items = DefaultChecklistItems.generateDefaultItems();
      
      expect(items.length, 10);
      expect(items[0].title, 'Emergency Exits');
      expect(items[1].title, 'Fire Safety Equipment');
      expect(items[2].title, 'Personal Protective Equipment');
      
      // All items should have unique IDs
      final ids = items.map((item) => item.id).toSet();
      expect(ids.length, 10);
      
      // All items should start as not checked
      for (final item in items) {
        expect(item.status, ChecklistItemStatus.notChecked);
        expect(item.isCompleted, false);
      }
    });

    test('CSV export service should be instantiable', () {
      final csvService = CsvExportService();
      expect(csvService, isNotNull);
      
      // Test file size formatting
      expect(csvService.formatFileSize(500), '500 B');
      expect(csvService.formatFileSize(1500), '1.5 KB');
      expect(csvService.formatFileSize(1500000), '1.4 MB');
    });

    test('Photo service should be instantiable', () {
      final photoService = PhotoService();
      expect(photoService, isNotNull);
      
      // Test file size formatting
      expect(photoService.formatFileSize(500), '500 B');
      expect(photoService.formatFileSize(1500), '1.5 KB');
      expect(photoService.formatFileSize(1500000), '1.4 MB');
      
      // Test size validation
      expect(photoService.isImageSizeValid(100000), true); // 100KB
      expect(photoService.isImageSizeValid(300000), false); // 300KB (over 250KB limit)
    });

    test('Session status transitions should work correctly', () {
      final session = SessionModel(
        id: 'test-session',
        type: SessionType.inspection,
        leaderId: 'test-leader',
        areaId: 'test-area',
        status: SessionStatus.active,
        inviteCode: 'TEST123',
        createdAt: DateTime.now(),
      );

      // Test status update
      final completedSession = session.copyWith(
        status: SessionStatus.completed,
        completedAt: DateTime.now(),
      );

      expect(completedSession.status, SessionStatus.completed);
      expect(completedSession.completedAt, isNotNull);
      expect(completedSession.id, session.id); // Other fields should remain the same
    });

    test('Finding severity levels should be ordered correctly', () {
      final severities = [
        FindingSeverity.low,
        FindingSeverity.medium,
        FindingSeverity.high,
        FindingSeverity.critical,
      ];

      expect(severities.length, 4);
      expect(severities[0], FindingSeverity.low);
      expect(severities[3], FindingSeverity.critical);
    });

    test('Checklist item status transitions should work correctly', () {
      final item = ChecklistItemModel(
        id: 'test-item',
        title: 'Test Item',
        description: 'Test description',
      );

      expect(item.status, ChecklistItemStatus.notChecked);
      expect(item.isCompleted, false);

      final passedItem = item.copyWith(
        status: ChecklistItemStatus.pass,
        completedAt: DateTime.now(),
      );

      expect(passedItem.status, ChecklistItemStatus.pass);
      expect(passedItem.isCompleted, true);
      expect(passedItem.isPassed, true);
      expect(passedItem.isFailed, false);

      final failedItem = item.copyWith(
        status: ChecklistItemStatus.fail,
        completedAt: DateTime.now(),
      );

      expect(failedItem.status, ChecklistItemStatus.fail);
      expect(failedItem.isCompleted, true);
      expect(failedItem.isPassed, false);
      expect(failedItem.isFailed, true);
    });
  });
}
