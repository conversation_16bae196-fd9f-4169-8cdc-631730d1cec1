/// Enum for checklist item status
enum ChecklistItemStatus {
  notChecked,
  pass,
  fail,
}

/// Model for individual checklist items
class ChecklistItemModel {
  final String id;
  final String title;
  final String description;
  final ChecklistItemStatus status;
  final String? notes;
  final String? photoPath;
  final DateTime? completedAt;

  const ChecklistItemModel({
    required this.id,
    required this.title,
    required this.description,
    this.status = ChecklistItemStatus.notChecked,
    this.notes,
    this.photoPath,
    this.completedAt,
  });

  /// Create ChecklistItemModel from map
  factory ChecklistItemModel.fromMap(Map<String, dynamic> map) {
    return ChecklistItemModel(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String,
      status: ChecklistItemStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ChecklistItemStatus.notChecked,
      ),
      notes: map['notes'] as String?,
      photoPath: map['photo_path'] as String?,
      completedAt: map['completed_at'] != null
          ? (map['completed_at'] is int
              ? DateTime.fromMillisecondsSinceEpoch(map['completed_at'] as int)
              : DateTime.parse(map['completed_at'] as String))
          : null,
    );
  }

  /// Convert ChecklistItemModel to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'status': status.name,
      'notes': notes,
      'photo_path': photoPath,
      'completed_at': completedAt?.millisecondsSinceEpoch,
    };
  }

  /// Create copy with updated fields
  ChecklistItemModel copyWith({
    String? id,
    String? title,
    String? description,
    ChecklistItemStatus? status,
    String? notes,
    String? photoPath,
    DateTime? completedAt,
  }) {
    return ChecklistItemModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      photoPath: photoPath ?? this.photoPath,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  /// Check if item is completed (either pass or fail)
  bool get isCompleted => status != ChecklistItemStatus.notChecked;

  /// Check if item passed
  bool get isPassed => status == ChecklistItemStatus.pass;

  /// Check if item failed
  bool get isFailed => status == ChecklistItemStatus.fail;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChecklistItemModel &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.status == status &&
        other.notes == notes &&
        other.photoPath == photoPath &&
        other.completedAt == completedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        description.hashCode ^
        status.hashCode ^
        notes.hashCode ^
        photoPath.hashCode ^
        completedAt.hashCode;
  }

  @override
  String toString() {
    return 'ChecklistItemModel(id: $id, title: $title, description: $description, status: $status, notes: $notes, photoPath: $photoPath, completedAt: $completedAt)';
  }
}

/// Predefined checklist items for safety inspections
class DefaultChecklistItems {
  static const List<Map<String, String>> items = [
    {
      'title': 'Emergency Exits',
      'description': 'Verify all emergency exits are clearly marked, unobstructed, and functional',
    },
    {
      'title': 'Fire Safety Equipment',
      'description': 'Check fire extinguishers, smoke detectors, and sprinkler systems are in working order',
    },
    {
      'title': 'Personal Protective Equipment',
      'description': 'Ensure required PPE is available, properly maintained, and being used correctly',
    },
    {
      'title': 'Electrical Safety',
      'description': 'Inspect electrical panels, cords, and outlets for damage or safety hazards',
    },
    {
      'title': 'Housekeeping & Cleanliness',
      'description': 'Assess general cleanliness, organization, and absence of slip/trip hazards',
    },
    {
      'title': 'Chemical Storage',
      'description': 'Verify proper storage, labeling, and handling of hazardous materials',
    },
    {
      'title': 'Machinery & Equipment',
      'description': 'Check that machinery guards are in place and equipment is properly maintained',
    },
    {
      'title': 'Ventilation Systems',
      'description': 'Ensure adequate ventilation and air quality in work areas',
    },
    {
      'title': 'Safety Signage',
      'description': 'Confirm safety signs are visible, legible, and appropriately placed',
    },
    {
      'title': 'First Aid Facilities',
      'description': 'Verify first aid kits are stocked and accessible, and emergency procedures are posted',
    },
  ];

  /// Generate default checklist items with unique IDs
  static List<ChecklistItemModel> generateDefaultItems() {
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      return ChecklistItemModel(
        id: 'default_${index + 1}',
        title: item['title']!,
        description: item['description']!,
      );
    }).toList();
  }
}
