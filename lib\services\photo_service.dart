import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// Service for handling photo capture and compression
class PhotoService {
  static const int _maxFileSizeBytes = 250 * 1024; // 250KB
  static const int _imageQuality = 85; // JPEG quality (0-100)
  
  final ImagePicker _picker = ImagePicker();

  /// Capture photo from camera with compression
  Future<String?> capturePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: _imageQuality,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image == null) return null;

      return await _processAndSaveImage(image);
    } catch (e) {
      debugPrint('Error capturing photo: $e');
      return null;
    }
  }

  /// Pick photo from gallery with compression
  Future<String?> pickFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: _imageQuality,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image == null) return null;

      return await _processAndSaveImage(image);
    } catch (e) {
      debugPrint('Error picking photo: $e');
      return null;
    }
  }

  /// Process and save image with compression
  Future<String?> _processAndSaveImage(XFile image) async {
    try {
      final File imageFile = File(image.path);
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Check if image is already under size limit
      if (imageBytes.length <= _maxFileSizeBytes) {
        return await _saveImageToAppDirectory(imageBytes, image.name);
      }

      // If image is too large, we need to compress it further
      // For now, we'll save it as is and let the UI handle the size warning
      // In a production app, you might want to use a package like flutter_image_compress
      return await _saveImageToAppDirectory(imageBytes, image.name);
    } catch (e) {
      debugPrint('Error processing image: $e');
      return null;
    }
  }

  /// Save image to app's documents directory
  Future<String> _saveImageToAppDirectory(Uint8List imageBytes, String originalName) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String imagesDir = path.join(appDir.path, 'images');
    
    // Create images directory if it doesn't exist
    await Directory(imagesDir).create(recursive: true);
    
    // Generate unique filename
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String extension = path.extension(originalName).toLowerCase();
    final String fileName = 'img_${timestamp}${extension.isEmpty ? '.jpg' : extension}';
    final String filePath = path.join(imagesDir, fileName);
    
    // Save image
    final File file = File(filePath);
    await file.writeAsBytes(imageBytes);
    
    return filePath;
  }

  /// Get image file size in bytes
  Future<int> getImageSize(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      debugPrint('Error getting image size: $e');
      return 0;
    }
  }

  /// Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Check if image size is within limit
  bool isImageSizeValid(int bytes) {
    return bytes <= _maxFileSizeBytes;
  }

  /// Delete image file
  Future<bool> deleteImage(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting image: $e');
      return false;
    }
  }

  /// Show photo source selection dialog
  Future<String?> showPhotoSourceDialog(BuildContext context) async {
    return await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Photo Source'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Camera'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final imagePath = await capturePhoto();
                  if (context.mounted) {
                    Navigator.of(context).pop(imagePath);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final imagePath = await pickFromGallery();
                  if (context.mounted) {
                    Navigator.of(context).pop(imagePath);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  /// Get maximum file size in bytes
  static int get maxFileSizeBytes => _maxFileSizeBytes;

  /// Get maximum file size formatted
  static String get maxFileSizeFormatted => '250 KB';
}
