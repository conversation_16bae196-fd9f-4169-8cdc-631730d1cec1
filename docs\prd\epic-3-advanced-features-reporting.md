# Epic 3 Advanced Features & Reporting
Deliver Premium features like multi-site support, advanced reporting, and AI duplicate detection to scale for enterprise needs and compliance.

## Story 3.1 [Medium] Multi-Site Support
As a Leader (Premium), I want to manage safety inspections across multiple facilities, so that I can maintain consistent safety standards enterprise-wide.

### Acceptance Criteria
- 3.1.1: Sites tab allows adding/editing Site names and switching between Sites.
- 3.1.2: Each Site links to Areas, stored in Firestore (`{id, name, areas: [area_id]}`).
- 3.1.3: Free tier limited to 1 Site; Premium supports multiple Sites.
- 3.1.4: E2E tests for Site switching and Area management.

## Story 3.2 [Medium] Advanced Reporting
As a Leader (Premium), I want comprehensive, customizable safety reports, so that I can meet regulatory compliance requirements and demonstrate safety program effectiveness.

### Acceptance Criteria
- 3.2.1: Review Hub supports PDF/CSV export with ISO 45001/Gemba templates (Premium).
- 3.2.2: Reports include audit trails, shareable via email or Google Drive/Dropbox (`googleapis`).
- 3.2.3: Free tier limited to basic CSV export.
- 3.2.4: E2E tests for report generation and sharing.

## Story 3.3 [Medium] AI Duplicate Detection
As a Leader (Premium), I want automated identification of duplicate safety findings, so that I can efficiently review inspection results and avoid redundant follow-up actions.

### Acceptance Criteria
- 3.3.1: MiniLM model (established in Story 2.2) analyzes text similarity between finding descriptions using semantic embeddings.
- 3.3.2: Image hash comparison algorithm detects visually similar photos with configurable similarity thresholds (85% default).
- 3.3.3: Duplicate detection pipeline combines text and image analysis with weighted scoring system.
- 3.3.4: Merge suggestions displayed in Review Hub with confidence scores and side-by-side comparison UI.
- 3.3.5: Leaders can accept/reject merge suggestions, with decisions stored in SQLite/Firestore for learning.
- 3.3.6: Batch processing capability for analyzing existing findings when feature is first enabled.
- 3.3.7: Unit tests for duplicate detection accuracy using curated test dataset (>90% precision target).
- 3.3.8: Performance optimization ensures duplicate analysis completes within 2 seconds for 100 findings.
