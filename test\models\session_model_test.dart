import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/session_model.dart';

void main() {
  group('SessionModel', () {
    late SessionModel testSession;

    setUp(() {
      testSession = SessionModel(
        id: 'test-session-id',
        type: SessionType.inspection,
        leaderId: 'leader-id',
        areaId: 'area-id',
        status: SessionStatus.active,
        inviteCode: 'ABC123',
        createdAt: DateTime(2024, 1, 1),
      );
    });

    test('should create SessionModel with all properties', () {
      expect(testSession.id, 'test-session-id');
      expect(testSession.type, SessionType.inspection);
      expect(testSession.leaderId, 'leader-id');
      expect(testSession.areaId, 'area-id');
      expect(testSession.status, SessionStatus.active);
      expect(testSession.inviteCode, 'ABC123');
      expect(testSession.createdAt, DateTime(2024, 1, 1));
    });

    test('should convert SessionModel to map correctly', () {
      final map = testSession.toMap();

      expect(map['id'], 'test-session-id');
      expect(map['type'], 'inspection');
      expect(map['leader_id'], 'leader-id');
      expect(map['area_id'], 'area-id');
      expect(map['status'], 'active');
      expect(map['invite_code'], 'ABC123');
      expect(map['created_at'], DateTime(2024, 1, 1).millisecondsSinceEpoch);
    });

    test('should create SessionModel from map correctly', () {
      final map = {
        'id': 'test-session-id',
        'type': 'inspection',
        'leader_id': 'leader-id',
        'area_id': 'area-id',
        'status': 'active',
        'invite_code': 'ABC123',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final session = SessionModel.fromMap(map);

      expect(session.id, 'test-session-id');
      expect(session.type, SessionType.inspection);
      expect(session.leaderId, 'leader-id');
      expect(session.areaId, 'area-id');
      expect(session.status, SessionStatus.active);
      expect(session.inviteCode, 'ABC123');
      expect(session.createdAt, DateTime(2024, 1, 1));
    });

    test('should handle invalid type gracefully', () {
      final map = {
        'id': 'test-session-id',
        'type': 'invalid-type',
        'leader_id': 'leader-id',
        'area_id': 'area-id',
        'status': 'active',
        'invite_code': 'ABC123',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final session = SessionModel.fromMap(map);
      expect(session.type, SessionType.inspection); // Should default to inspection
    });

    test('should handle invalid status gracefully', () {
      final map = {
        'id': 'test-session-id',
        'type': 'inspection',
        'leader_id': 'leader-id',
        'area_id': 'area-id',
        'status': 'invalid-status',
        'invite_code': 'ABC123',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final session = SessionModel.fromMap(map);
      expect(session.status, SessionStatus.active); // Should default to active
    });

    test('should create copy with updated fields', () {
      final updatedSession = testSession.copyWith(
        status: SessionStatus.completed,
        inviteCode: 'XYZ789',
      );

      expect(updatedSession.id, testSession.id);
      expect(updatedSession.type, testSession.type);
      expect(updatedSession.leaderId, testSession.leaderId);
      expect(updatedSession.areaId, testSession.areaId);
      expect(updatedSession.status, SessionStatus.completed);
      expect(updatedSession.inviteCode, 'XYZ789');
      expect(updatedSession.createdAt, testSession.createdAt);
    });

    test('should implement equality correctly', () {
      final session1 = SessionModel(
        id: 'test-id',
        type: SessionType.inspection,
        leaderId: 'leader-id',
        areaId: 'area-id',
        status: SessionStatus.active,
        inviteCode: 'ABC123',
        createdAt: DateTime(2024, 1, 1),
      );

      final session2 = SessionModel(
        id: 'test-id',
        type: SessionType.inspection,
        leaderId: 'leader-id',
        areaId: 'area-id',
        status: SessionStatus.active,
        inviteCode: 'ABC123',
        createdAt: DateTime(2024, 1, 1),
      );

      final session3 = session1.copyWith(status: SessionStatus.completed);

      expect(session1, equals(session2));
      expect(session1, isNot(equals(session3)));
    });

    test('should have consistent hashCode', () {
      final session1 = SessionModel(
        id: 'test-id',
        type: SessionType.inspection,
        leaderId: 'leader-id',
        areaId: 'area-id',
        status: SessionStatus.active,
        inviteCode: 'ABC123',
        createdAt: DateTime(2024, 1, 1),
      );

      final session2 = SessionModel(
        id: 'test-id',
        type: SessionType.inspection,
        leaderId: 'leader-id',
        areaId: 'area-id',
        status: SessionStatus.active,
        inviteCode: 'ABC123',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(session1.hashCode, equals(session2.hashCode));
    });

    test('should have meaningful toString', () {
      final toString = testSession.toString();
      expect(toString, contains('SessionModel'));
      expect(toString, contains('test-session-id'));
      expect(toString, contains('inspection'));
    });
  });
}
