import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/database_service.dart';
import '../services/firebase_service.dart';
import '../utils/app_theme.dart';

/// Widget that displays system health check information
class HealthCheckWidget extends StatefulWidget {
  const HealthCheckWidget({super.key});

  @override
  State<HealthCheckWidget> createState() => _HealthCheckWidgetState();
}

class _HealthCheckWidgetState extends State<HealthCheckWidget> {
  bool _isLoading = true;
  final Map<String, HealthStatus> _healthChecks = {};

  @override
  void initState() {
    super.initState();
    _performHealthChecks();
  }

  Future<void> _performHealthChecks() async {
    setState(() {
      _isLoading = true;
      _healthChecks.clear();
    });

    // Check connectivity
    await _checkConnectivity();

    // Check local database
    await _checkLocalDatabase();

    // Check Firebase connection
    await _checkFirebaseConnection();

    // Check authentication service
    await _checkAuthenticationService();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      _healthChecks['Connectivity'] =
          connectivityResult != ConnectivityResult.none
              ? HealthStatus.healthy
              : HealthStatus.warning;
    } catch (e) {
      _healthChecks['Connectivity'] = HealthStatus.error;
    }
  }

  Future<void> _checkLocalDatabase() async {
    try {
      final db = await DatabaseService.database;
      // Try a simple query to verify database is working
      await db.rawQuery('SELECT 1');
      _healthChecks['Local Database'] = HealthStatus.healthy;
    } catch (e) {
      _healthChecks['Local Database'] = HealthStatus.error;
    }
  }

  Future<void> _checkFirebaseConnection() async {
    try {
      if (FirebaseService.isAuthenticated) {
        _healthChecks['Firebase'] = HealthStatus.healthy;
      } else {
        _healthChecks['Firebase'] = HealthStatus.warning;
      }
    } catch (e) {
      _healthChecks['Firebase'] = HealthStatus.error;
    }
  }

  Future<void> _checkAuthenticationService() async {
    try {
      final currentUser = FirebaseService.currentUser;
      _healthChecks['Authentication'] =
          currentUser != null ? HealthStatus.healthy : HealthStatus.warning;
    } catch (e) {
      _healthChecks['Authentication'] = HealthStatus.error;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'System Health Check',
                  style: AppTheme.headingSmall.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                IconButton(
                  onPressed: _performHealthChecks,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                  tooltip: 'Refresh health check',
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(AppTheme.spacingLarge),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              Column(
                children: _healthChecks.entries.map((entry) {
                  return _buildHealthCheckItem(
                    context,
                    entry.key,
                    entry.value,
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthCheckItem(
    BuildContext context,
    String name,
    HealthStatus status,
  ) {
    IconData icon;
    Color color;
    String statusText;

    switch (status) {
      case HealthStatus.healthy:
        icon = Icons.check_circle;
        color = Theme.of(context).colorScheme.secondary;
        statusText = 'Healthy';
        break;
      case HealthStatus.warning:
        icon = Icons.warning;
        color = AppTheme.warningColor;
        statusText = 'Warning';
        break;
      case HealthStatus.error:
        icon = Icons.error;
        color = Theme.of(context).colorScheme.error;
        statusText = 'Error';
        break;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingSmall),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: AppTheme.spacingMedium),
          Expanded(
            child: Text(
              name,
              style: AppTheme.bodyMedium,
            ),
          ),
          Text(
            statusText,
            style: AppTheme.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Enum for health check status
enum HealthStatus {
  healthy,
  warning,
  error,
}
