import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:safestride/services/auth_service.dart';
import 'package:safestride/repositories/user_repository.dart';
import 'package:safestride/models/user_model.dart';

// Generate mocks
@GenerateMocks([UserRepository, User, UserCredential])
import 'auth_service_test.mocks.dart';

void main() {
  group('AuthService', () {
    late AuthService authService;
    late MockUserRepository mockUserRepository;

    setUp(() {
      mockUserRepository = MockUserRepository();
      authService = AuthService(mockUserRepository);
    });

    group('signInWithEmailAndPassword', () {
      test('should return user model on successful sign in', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final userModel = UserModel(
          id: 'test-uid',
          email: email,
          name: 'Test User',
          role: UserRole.observer,
          subscription: SubscriptionType.free,
          createdAt: DateTime.now(),
        );

        when(mockUserRepository.getUserById('test-uid'))
            .thenAnswer((_) async => userModel);
        when(mockUserRepository.cacheUserCredentials(email, password))
            .thenAnswer((_) async => {});

        // Note: Firebase Auth methods would need to be mocked in a real test
        // For now, we'll test the repository interactions

        // Verify repository methods are called
        verify(mockUserRepository.getUserById('test-uid')).called(0);
      });

      test('should handle authentication errors gracefully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(mockUserRepository.getUserById(any))
            .thenThrow(Exception('User not found'));

        // Act & Assert
        expect(
          () async => await authService.signInWithEmailAndPassword(
            email: email,
            password: password,
          ),
          throwsA(isA<String>()),
        );
      });
    });

    group('signUpWithEmailAndPassword', () {
      test('should create new user on successful sign up', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'New User';

        when(mockUserRepository.createUser(any))
            .thenAnswer((_) async => {});
        when(mockUserRepository.cacheUserCredentials(email, password))
            .thenAnswer((_) async => {});

        // Note: Firebase Auth methods would need to be mocked in a real test
        // For now, we'll verify the user model structure

        // Verify user model properties
        const expectedRole = UserRole.observer;
        const expectedSubscription = SubscriptionType.free;

        expect(expectedRole, equals(UserRole.observer));
        expect(expectedSubscription, equals(SubscriptionType.free));
      });
    });

    group('signOut', () {
      test('should clear cached credentials on sign out', () async {
        // Arrange
        when(mockUserRepository.clearCachedCredentials())
            .thenAnswer((_) async => {});

        // Act
        await authService.signOut();

        // Assert
        verify(mockUserRepository.clearCachedCredentials()).called(1);
      });
    });

    group('tryOfflineAuthentication', () {
      test('should return user model for valid offline credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        final userModel = UserModel(
          id: 'test-uid',
          email: email,
          name: 'Test User',
          role: UserRole.leader,
          subscription: SubscriptionType.premium,
          createdAt: DateTime.now(),
        );

        when(mockUserRepository.authenticateOffline(email, password))
            .thenAnswer((_) async => userModel);

        // Act
        final result = await authService.tryOfflineAuthentication(
          email: email,
          password: password,
        );

        // Assert
        expect(result, equals(userModel));
        verify(mockUserRepository.authenticateOffline(email, password)).called(1);
      });

      test('should return null for invalid offline credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(mockUserRepository.authenticateOffline(email, password))
            .thenAnswer((_) async => null);

        // Act
        final result = await authService.tryOfflineAuthentication(
          email: email,
          password: password,
        );

        // Assert
        expect(result, isNull);
        verify(mockUserRepository.authenticateOffline(email, password)).called(1);
      });
    });

    group('resetPassword', () {
      test('should not throw exception for valid email', () async {
        // Arrange
        const email = '<EMAIL>';

        // Act & Assert
        expect(
          () async => await authService.resetPassword(email),
          returnsNormally,
        );
      });
    });

    group('loading state', () {
      test('should track loading state correctly', () {
        // Arrange & Act
        expect(authService.isLoading, isFalse);

        // Note: In a real test, we would trigger an async operation
        // and verify the loading state changes appropriately
      });
    });

    group('authentication state', () {
      test('should track authentication state correctly', () {
        // Arrange & Act
        expect(authService.isAuthenticated, isFalse);
        expect(authService.currentUser, isNull);

        // Note: In a real test, we would sign in a user
        // and verify the authentication state changes
      });
    });

    group('offline state', () {
      test('should track offline state correctly', () {
        // Arrange & Act
        expect(authService.isOffline, isFalse);

        // Note: In a real test, we would simulate network changes
        // and verify the offline state updates correctly
      });
    });
  });

  group('UserModel', () {
    test('should create user model with correct properties', () {
      // Arrange
      final now = DateTime.now();
      const id = 'test-id';
      const email = '<EMAIL>';
      const name = 'Test User';
      const role = UserRole.leader;
      const subscription = SubscriptionType.premium;

      // Act
      final user = UserModel(
        id: id,
        email: email,
        name: name,
        role: role,
        subscription: subscription,
        createdAt: now,
      );

      // Assert
      expect(user.id, equals(id));
      expect(user.email, equals(email));
      expect(user.name, equals(name));
      expect(user.role, equals(role));
      expect(user.subscription, equals(subscription));
      expect(user.createdAt, equals(now));
    });

    test('should convert to and from map correctly', () {
      // Arrange
      final now = DateTime.now();
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.observer,
        subscription: SubscriptionType.free,
        createdAt: now,
      );

      // Act
      final map = user.toMap();
      final userFromMap = UserModel.fromMap(map);

      // Assert
      expect(userFromMap.id, equals(user.id));
      expect(userFromMap.email, equals(user.email));
      expect(userFromMap.name, equals(user.name));
      expect(userFromMap.role, equals(user.role));
      expect(userFromMap.subscription, equals(user.subscription));
      expect(userFromMap.createdAt.millisecondsSinceEpoch, 
             equals(user.createdAt.millisecondsSinceEpoch));
    });

    test('should create copy with updated fields', () {
      // Arrange
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.observer,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      // Act
      final updatedUser = user.copyWith(
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
      );

      // Assert
      expect(updatedUser.id, equals(user.id));
      expect(updatedUser.email, equals(user.email));
      expect(updatedUser.name, equals(user.name));
      expect(updatedUser.role, equals(UserRole.leader));
      expect(updatedUser.subscription, equals(SubscriptionType.premium));
      expect(updatedUser.createdAt, equals(user.createdAt));
    });
  });
}
