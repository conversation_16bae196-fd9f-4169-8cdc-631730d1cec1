# Epic 1 Foundation & Core Infrastructure

Establish the core app with Firebase setup, role selection, login, and Solo Walkabout functionality to provide immediate value for individual safety inspections while setting up infrastructure for future features.

## Story 1.1 [Critical] Project Setup & Firebase Integration

As a developer, I want to establish the foundational app infrastructure, so that users can securely access the app and their data is reliably stored both online and offline.

### Acceptance Criteria

- 1.1.1: Flutter project created with iOS 12.0+ and Android 8.0+ support.
- 1.1.2: Firebase Authentication (email/password, Google SSO) integrated with offline credential caching in SQLite.
- 1.1.3: Firestore configured for storing users, sessions, findings, and areas.
- 1.1.4: SQLite (`sqflite`) implemented for offline caching of login credentials and basic data models.
- 1.1.5: Basic health-check route displays a “Welcome to SafeStride” screen.
- 1.1.6: Unit tests for Firebase and SQLite integration.

## Story 1.2 [Critical] Role Selection & Login UI

As a user, I want to securely access the app and specify my role, so that I can use features appropriate to my safety inspection responsibilities.

### Acceptance Criteria

- 1.2.1: Login screen supports email/password and SSO (Google) via Firebase Authentication.
- 1.2.2: Role Selection screen displays post-login with “Leader” and “Observer” options (large touch targets, high-contrast).
- 1.2.3: Offline credential caching allows login in poor connectivity areas.
- 1.2.4: First-time users see a 30-day Premium trial prompt and onboarding tutorial.
- 1.2.5: E2E tests for login and role selection flows.
- 1.2.6: WCAG-compliant UI (large fonts, color-blind-friendly).

## Story 1.3 [Critical] Solo Walkabout & Area Management

As a Leader, I want to conduct individual safety inspections and organize locations, so that I can systematically document hazards and share findings with stakeholders.

### Acceptance Criteria

- 1.3.1: Home screen offers “Solo Walkabout” option post-Leader role selection.
- 1.3.2: Area selection dropdown lists Areas (default Site in Free tier) from SQLite/Firestore.
- 1.3.3: Leaders can add/edit Area names (e.g., “Warehouse A” to “Storage Zone A”).
- 1.3.4: Checklist form includes 10 items (Pass/Fail, notes, 250KB photos via `image_picker`).
- 1.3.5: Hazard form includes description, severity (Low/Medium/High), and photo.
- 1.3.6: Findings saved in SQLite, synced to Firestore when online, with sync status UI.
- 1.3.7: CSV export shareable via email.
- 1.3.8: Unit and E2E tests for walkabout creation and export.
