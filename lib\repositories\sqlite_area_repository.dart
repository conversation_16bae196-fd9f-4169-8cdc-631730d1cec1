import 'package:sqflite/sqflite.dart';
import '../models/area_model.dart';
import '../services/database_service.dart';
import 'area_repository.dart';

/// SQLite implementation of AreaRepository
class SqliteAreaRepository implements AreaRepository {
  static const String _tableName = 'areas';

  @override
  Future<AreaModel?> getAreaById(String id) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return AreaModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<void> createArea(AreaModel area) async {
    final db = await DatabaseService.database;
    await db.insert(
      _tableName,
      area.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> updateArea(AreaModel area) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      area.toMap(),
      where: 'id = ?',
      whereArgs: [area.id],
    );
  }

  @override
  Future<void> deleteArea(String id) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<AreaModel>> getAreasBySite(String siteId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'site_id = ?',
      whereArgs: [siteId],
      orderBy: 'name ASC',
    );
    return maps.map((map) => AreaModel.fromMap(map)).toList();
  }

  @override
  Future<List<AreaModel>> getAllAreas() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      orderBy: 'name ASC',
    );
    return maps.map((map) => AreaModel.fromMap(map)).toList();
  }

  @override
  Future<List<AreaModel>> getUnsyncedAreas() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => AreaModel.fromMap(map)).toList();
  }

  @override
  Future<void> markAreaAsSynced(String id) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<int> getAreasCountBySite(String siteId) async {
    final db = await DatabaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM $_tableName WHERE site_id = ?',
      [siteId],
    );
    return result.first['count'] as int;
  }

  @override
  Future<List<AreaModel>> searchAreasByName(String query) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'name LIKE ?',
      whereArgs: ['%$query%'],
      orderBy: 'name ASC',
    );
    return maps.map((map) => AreaModel.fromMap(map)).toList();
  }
}
