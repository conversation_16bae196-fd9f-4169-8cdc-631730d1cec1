import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/models/user_model.dart';
import 'package:safestride/models/area_model.dart';
import 'package:safestride/models/site_model.dart';
import 'package:safestride/providers/auth_provider.dart';
import 'package:safestride/repositories/repository_factory.dart';
import 'package:safestride/screens/home_screen.dart';
import 'package:safestride/screens/area_selection_screen.dart';
import 'package:safestride/screens/solo_walkabout_screen.dart';
import 'package:safestride/screens/checklist_screen.dart';
import 'package:safestride/screens/hazard_form_screen.dart';
import 'package:safestride/services/database_service.dart';

import 'solo_walkabout_integration_test.mocks.dart';

@GenerateMocks([AuthProvider, RepositoryFactory])
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Solo Walkabout Integration Tests', () {
    late MockAuthProvider mockAuthProvider;
    late UserModel testUser;
    late SiteModel testSite;
    late AreaModel testArea;

    setUpAll(() async {
      // Initialize test database
      await DatabaseService.clearAllData();
    });

    setUp(() {
      mockAuthProvider = MockAuthProvider();
      
      testUser = UserModel(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      testSite = SiteModel(
        id: 'test-site-id',
        name: 'Test Site',
        address: 'Test Address',
        ownerId: testUser.id,
        createdAt: DateTime.now(),
      );

      testArea = AreaModel(
        id: 'test-area-id',
        name: 'Test Area',
        siteId: testSite.id,
        description: 'Test area for integration testing',
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(testUser);
    });

    Widget createTestApp(Widget home) {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: home,
        ),
      );
    }

    testWidgets('Complete solo walkabout workflow - Leader user', (WidgetTester tester) async {
      // Start from home screen
      await tester.pumpWidget(createTestApp(const HomeScreen()));
      await tester.pumpAndSettle();

      // Verify home screen displays correctly for leader
      expect(find.text('Welcome, Test User'), findsOneWidget);
      expect(find.text('Role: Leader'), findsOneWidget);
      expect(find.text('Solo Walkabout'), findsOneWidget);

      // Tap Solo Walkabout button
      await tester.tap(find.text('Solo Walkabout'));
      await tester.pumpAndSettle();

      // Should navigate to area selection screen
      expect(find.text('Select Area'), findsOneWidget);
      
      // Note: In a real integration test, we would need to set up the database
      // with test data. For this example, we'll verify the UI structure.
      
      // Verify area selection screen elements
      expect(find.byIcon(Icons.add), findsOneWidget); // Add area button
      expect(find.text('Start Solo Walkabout'), findsOneWidget);
    });

    testWidgets('Area management workflow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp(const AreaSelectionScreen()));
      await tester.pumpAndSettle();

      // Verify area selection screen
      expect(find.text('Select Area'), findsOneWidget);
      
      // Tap add area button
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Verify add area dialog appears
      expect(find.text('Add New Area'), findsOneWidget);
      expect(find.text('Area Name'), findsOneWidget);
      expect(find.text('Description (Optional)'), findsOneWidget);

      // Fill in area details
      await tester.enterText(find.byType(TextFormField).first, 'Test Area');
      await tester.enterText(find.byType(TextFormField).last, 'Test description');

      // Tap save button
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Dialog should close (in real test, area would be saved to database)
    });

    testWidgets('Checklist form workflow', (WidgetTester tester) async {
      // Create checklist screen with test data
      await tester.pumpWidget(createTestApp(
        ChecklistScreen(
          sessionId: 'test-session-id',
          areaName: 'Test Area',
          onChecklistCompleted: (items) {},
        ),
      ));
      await tester.pumpAndSettle();

      // Verify checklist screen
      expect(find.text('Checklist - Test Area'), findsOneWidget);
      expect(find.text('Progress: 0 / 10'), findsOneWidget);

      // Verify checklist items are displayed
      expect(find.text('Emergency Exits'), findsOneWidget);
      expect(find.text('Fire Safety Equipment'), findsOneWidget);
      expect(find.text('Personal Protective Equipment'), findsOneWidget);

      // Test Pass/Fail buttons
      final passButtons = find.text('Pass');
      expect(passButtons, findsNWidgets(10)); // 10 checklist items

      // Tap first Pass button
      await tester.tap(passButtons.first);
      await tester.pumpAndSettle();

      // Progress should update
      expect(find.text('Progress: 1 / 10'), findsOneWidget);

      // Test adding notes
      final notesFields = find.byType(TextField);
      await tester.enterText(notesFields.first, 'Test notes for first item');
      await tester.pumpAndSettle();

      // Complete all items to enable completion button
      for (int i = 1; i < 10; i++) {
        await tester.tap(passButtons.at(i));
        await tester.pumpAndSettle();
      }

      // Verify completion button is enabled
      expect(find.text('Complete Checklist'), findsOneWidget);
      
      final completeButton = find.widgetWithText(ElevatedButton, 'Complete Checklist');
      expect(tester.widget<ElevatedButton>(completeButton).onPressed, isNotNull);
    });

    testWidgets('Hazard form workflow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp(
        HazardFormScreen(
          sessionId: 'test-session-id',
          areaName: 'Test Area',
          onHazardSaved: (hazard) {},
        ),
      ));
      await tester.pumpAndSettle();

      // Verify hazard form screen
      expect(find.text('Report Hazard'), findsOneWidget);
      expect(find.text('Area: Test Area'), findsOneWidget);

      // Test form fields
      expect(find.text('Hazard Description'), findsOneWidget);
      expect(find.text('Severity Level'), findsOneWidget);
      expect(find.text('Category'), findsOneWidget);
      expect(find.text('Photo Evidence'), findsOneWidget);

      // Fill in hazard description
      await tester.enterText(
        find.byType(TextFormField).first,
        'Test hazard description for integration testing',
      );

      // Select severity level (Medium)
      await tester.tap(find.text('Medium'));
      await tester.pumpAndSettle();

      // Select category from dropdown
      await tester.tap(find.byType(DropdownButtonFormField));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Safety').last);
      await tester.pumpAndSettle();

      // Verify save button is enabled
      expect(find.text('Save Hazard'), findsOneWidget);
      
      final saveButton = find.widgetWithText(ElevatedButton, 'Save Hazard');
      expect(tester.widget<ElevatedButton>(saveButton).onPressed, isNotNull);
    });

    testWidgets('Solo walkabout session workflow', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp(
        SoloWalkaboutScreen(area: testArea),
      ));
      await tester.pumpAndSettle();

      // Verify solo walkabout screen
      expect(find.text('Solo Walkabout - Test Area'), findsOneWidget);
      expect(find.text('Inspection Session'), findsOneWidget);
      expect(find.text('Area: Test Area'), findsOneWidget);

      // Verify action cards
      expect(find.text('Safety Checklist'), findsOneWidget);
      expect(find.text('Complete 10-point safety inspection checklist'), findsOneWidget);
      expect(find.text('Report Hazards'), findsOneWidget);
      expect(find.text('Document safety hazards and concerns'), findsOneWidget);

      // Test checklist navigation
      await tester.tap(find.text('Safety Checklist'));
      await tester.pumpAndSettle();

      // Should navigate to checklist screen
      expect(find.text('Checklist - Test Area'), findsOneWidget);

      // Go back to solo walkabout screen
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Test hazard reporting navigation
      await tester.tap(find.text('Report Hazards'));
      await tester.pumpAndSettle();

      // Should navigate to hazard form screen
      expect(find.text('Report Hazard'), findsOneWidget);
    });

    testWidgets('Observer user restrictions', (WidgetTester tester) async {
      // Create observer user
      final observerUser = testUser.copyWith(role: UserRole.observer);
      when(mockAuthProvider.currentUser).thenReturn(observerUser);

      await tester.pumpWidget(createTestApp(const HomeScreen()));
      await tester.pumpAndSettle();

      // Verify observer sees different content
      expect(find.text('Welcome, Test User'), findsOneWidget);
      expect(find.text('Role: Observer'), findsOneWidget);
      
      // Solo Walkabout should not be available for observers
      expect(find.text('Solo Walkabout'), findsNothing);
      
      // But other features should be available
      expect(find.text('Group Walkabout'), findsOneWidget);
      expect(find.text('Export Data'), findsOneWidget);
    });

    testWidgets('Subscription status display', (WidgetTester tester) async {
      // Test free subscription
      await tester.pumpWidget(createTestApp(const HomeScreen()));
      await tester.pumpAndSettle();

      expect(find.text('Free Account'), findsOneWidget);
      expect(find.byIcon(Icons.star_border), findsOneWidget);

      // Test premium subscription
      final premiumUser = testUser.copyWith(subscription: SubscriptionType.premium);
      when(mockAuthProvider.currentUser).thenReturn(premiumUser);

      await tester.pumpWidget(createTestApp(const HomeScreen()));
      await tester.pumpAndSettle();

      expect(find.text('Premium Account'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
    });

    testWidgets('Navigation flow validation', (WidgetTester tester) async {
      // Test complete navigation flow
      await tester.pumpWidget(createTestApp(const HomeScreen()));
      await tester.pumpAndSettle();

      // Home -> Area Selection
      await tester.tap(find.text('Solo Walkabout'));
      await tester.pumpAndSettle();
      expect(find.text('Select Area'), findsOneWidget);

      // Back to Home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.text('Welcome, Test User'), findsOneWidget);

      // Home -> Export
      await tester.tap(find.text('Export Data'));
      await tester.pumpAndSettle();
      expect(find.text('Export Data'), findsOneWidget);

      // Back to Home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      expect(find.text('Welcome, Test User'), findsOneWidget);
    });
  });
}
