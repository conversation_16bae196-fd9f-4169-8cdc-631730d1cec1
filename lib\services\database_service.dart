import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

/// Service class for SQLite database operations
class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'safestride.db';
  static const int _databaseVersion = 3;

  /// Set database for testing purposes
  static set testDatabase(Database? db) {
    _database = db;
  }

  /// Get database instance
  static Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  static Future<Database> _initDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, _databaseName);

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  /// Create database tables
  static Future<void> _onCreate(Database db, int version) async {
    await _createTables(db);
  }

  /// Handle database upgrades
  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    // Handle database schema migrations here
    if (oldVersion < 2 && newVersion >= 2) {
      // Add sites and areas tables
      await db.execute('''
        CREATE TABLE sites (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          owner_id TEXT,
          address TEXT,
          description TEXT,
          latitude REAL,
          longitude REAL,
          created_at INTEGER,
          synced INTEGER DEFAULT 0
        )
      ''');

      await db.execute('''
        CREATE TABLE areas (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          site_id TEXT,
          description TEXT,
          qr_code TEXT,
          created_at INTEGER,
          synced INTEGER DEFAULT 0,
          FOREIGN KEY (site_id) REFERENCES sites(id)
        )
      ''');

      // Add indexes
      await db.execute('CREATE INDEX idx_sites_owner ON sites(owner_id)');
      await db.execute('CREATE INDEX idx_areas_site ON areas(site_id)');
    }

    if (oldVersion < 3 && newVersion >= 3) {
      // Add checklist items table
      await db.execute('''
        CREATE TABLE checklist_items (
          id TEXT PRIMARY KEY,
          session_id TEXT,
          title TEXT NOT NULL,
          description TEXT,
          status TEXT,
          notes TEXT,
          photo_path TEXT,
          completed_at INTEGER,
          item_order INTEGER,
          synced INTEGER DEFAULT 0,
          FOREIGN KEY (session_id) REFERENCES sessions(id)
        )
      ''');

      await db.execute(
          'CREATE INDEX idx_checklist_session ON checklist_items(session_id)');

      // Add completed_at column to sessions table
      await db.execute('ALTER TABLE sessions ADD COLUMN completed_at INTEGER');
    }
  }

  /// Create all database tables
  static Future<void> _createTables(Database db) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        name TEXT,
        role TEXT,
        subscription TEXT,
        created_at INTEGER,
        synced INTEGER DEFAULT 0
      )
    ''');

    // Sites table
    await db.execute('''
      CREATE TABLE sites (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        owner_id TEXT,
        address TEXT,
        description TEXT,
        latitude REAL,
        longitude REAL,
        created_at INTEGER,
        synced INTEGER DEFAULT 0
      )
    ''');

    // Areas table
    await db.execute('''
      CREATE TABLE areas (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        site_id TEXT,
        description TEXT,
        qr_code TEXT,
        created_at INTEGER,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (site_id) REFERENCES sites(id)
      )
    ''');

    // Sessions table
    await db.execute('''
      CREATE TABLE sessions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        leader_id TEXT,
        area_id TEXT,
        status TEXT,
        invite_code TEXT,
        created_at INTEGER,
        completed_at INTEGER,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (area_id) REFERENCES areas(id)
      )
    ''');

    // Findings table
    await db.execute('''
      CREATE TABLE findings (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        description TEXT,
        severity TEXT,
        category TEXT,
        photo_path TEXT,
        author_id TEXT,
        status TEXT,
        created_at INTEGER,
        resolved_at INTEGER,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (session_id) REFERENCES sessions(id)
      )
    ''');

    // Checklist items table
    await db.execute('''
      CREATE TABLE checklist_items (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        title TEXT NOT NULL,
        description TEXT,
        status TEXT,
        notes TEXT,
        photo_path TEXT,
        completed_at INTEGER,
        item_order INTEGER,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (session_id) REFERENCES sessions(id)
      )
    ''');

    // Cached credentials table for offline authentication
    await db.execute('''
      CREATE TABLE cached_credentials (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        salt TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )
    ''');

    // Sync queue table for offline operations
    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation_type TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        retry_count INTEGER DEFAULT 0
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_users_email ON users(email)');
    await db.execute('CREATE INDEX idx_sites_owner ON sites(owner_id)');
    await db.execute('CREATE INDEX idx_areas_site ON areas(site_id)');
    await db.execute('CREATE INDEX idx_sessions_leader ON sessions(leader_id)');
    await db.execute('CREATE INDEX idx_sessions_area ON sessions(area_id)');
    await db
        .execute('CREATE INDEX idx_findings_session ON findings(session_id)');
    await db.execute(
        'CREATE INDEX idx_checklist_session ON checklist_items(session_id)');
    await db.execute(
        'CREATE INDEX idx_sync_queue_created ON sync_queue(created_at)');
  }

  /// Close database connection
  static Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  /// Clear all data (for testing purposes)
  static Future<void> clearAllData() async {
    final db = await database;
    await db.delete('users');
    await db.delete('sites');
    await db.delete('areas');
    await db.delete('sessions');
    await db.delete('findings');
    await db.delete('checklist_items');
    await db.delete('cached_credentials');
    await db.delete('sync_queue');
  }

  /// Set test database (for testing purposes only)
  static void setTestDatabase(Database testDb) {
    _database = testDb;
  }

  /// Get database path for debugging
  static Future<String> getDatabasePath() async {
    final databasePath = await getDatabasesPath();
    return join(databasePath, _databaseName);
  }
}
