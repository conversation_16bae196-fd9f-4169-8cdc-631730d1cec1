import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/area_model.dart';

void main() {
  group('AreaModel', () {
    test('should create AreaModel with required fields', () {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(area.id, 'test-id');
      expect(area.name, 'Test Area');
      expect(area.siteId, 'site-id');
      expect(area.description, isNull);
      expect(area.qrCode, isNull);
      expect(area.createdAt, DateTime(2024, 1, 1));
    });

    test('should create AreaModel with all fields', () {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        description: 'Test description',
        qrCode: 'QR123',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(area.id, 'test-id');
      expect(area.name, 'Test Area');
      expect(area.siteId, 'site-id');
      expect(area.description, 'Test description');
      expect(area.qrCode, 'QR123');
      expect(area.createdAt, DateTime(2024, 1, 1));
    });

    test('should convert to map correctly', () {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        description: 'Test description',
        qrCode: 'QR123',
        createdAt: DateTime(2024, 1, 1),
      );

      final map = area.toMap();

      expect(map['id'], 'test-id');
      expect(map['name'], 'Test Area');
      expect(map['site_id'], 'site-id');
      expect(map['description'], 'Test description');
      expect(map['qr_code'], 'QR123');
      expect(map['created_at'], DateTime(2024, 1, 1).millisecondsSinceEpoch);
    });

    test('should create from map correctly', () {
      final map = {
        'id': 'test-id',
        'name': 'Test Area',
        'site_id': 'site-id',
        'description': 'Test description',
        'qr_code': 'QR123',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final area = AreaModel.fromMap(map);

      expect(area.id, 'test-id');
      expect(area.name, 'Test Area');
      expect(area.siteId, 'site-id');
      expect(area.description, 'Test description');
      expect(area.qrCode, 'QR123');
      expect(area.createdAt, DateTime(2024, 1, 1));
    });

    test('should create from map with string date', () {
      final map = {
        'id': 'test-id',
        'name': 'Test Area',
        'site_id': 'site-id',
        'description': null,
        'qr_code': null,
        'created_at': '2024-01-01T00:00:00.000Z',
      };

      final area = AreaModel.fromMap(map);

      expect(area.id, 'test-id');
      expect(area.name, 'Test Area');
      expect(area.siteId, 'site-id');
      expect(area.description, isNull);
      expect(area.qrCode, isNull);
      expect(area.createdAt, DateTime.parse('2024-01-01T00:00:00.000Z'));
    });

    test('should copy with updated fields', () {
      final original = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      final updated = original.copyWith(
        name: 'Updated Area',
        description: 'New description',
      );

      expect(updated.id, 'test-id');
      expect(updated.name, 'Updated Area');
      expect(updated.siteId, 'site-id');
      expect(updated.description, 'New description');
      expect(updated.qrCode, isNull);
      expect(updated.createdAt, DateTime(2024, 1, 1));
    });

    test('should implement equality correctly', () {
      final area1 = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      final area2 = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      final area3 = AreaModel(
        id: 'different-id',
        name: 'Test Area',
        siteId: 'site-id',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(area1, equals(area2));
      expect(area1, isNot(equals(area3)));
      expect(area1.hashCode, equals(area2.hashCode));
      expect(area1.hashCode, isNot(equals(area3.hashCode)));
    });

    test('should convert to string correctly', () {
      final area = AreaModel(
        id: 'test-id',
        name: 'Test Area',
        siteId: 'site-id',
        description: 'Test description',
        qrCode: 'QR123',
        createdAt: DateTime(2024, 1, 1),
      );

      final string = area.toString();

      expect(string, contains('test-id'));
      expect(string, contains('Test Area'));
      expect(string, contains('site-id'));
      expect(string, contains('Test description'));
      expect(string, contains('QR123'));
    });
  });
}
