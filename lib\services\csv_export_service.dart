import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path/path.dart' as path;
import '../models/session_model.dart';
import '../models/finding_model.dart';
import '../models/checklist_item_model.dart';
import '../models/area_model.dart';
import '../models/site_model.dart';
import '../repositories/repository_factory.dart';

/// Service for exporting inspection data to CSV format
class CsvExportService {
  final RepositoryFactory _repositoryFactory = RepositoryFactory();

  /// Export session data to CSV format
  Future<String> exportSessionToCsv({
    required String sessionId,
    bool includeFindings = true,
    bool includeChecklist = true,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      // Get session data
      final sessionRepository = _repositoryFactory.getSessionRepository();
      final session = await sessionRepository.getSessionById(sessionId);
      
      if (session == null) {
        throw Exception('Session not found');
      }

      onProgress?.call(0.2);

      // Get area and site data
      final areaRepository = _repositoryFactory.getAreaRepository();
      final siteRepository = _repositoryFactory.getSiteRepository();
      
      final area = await areaRepository.getAreaById(session.areaId);
      final site = area != null && area.siteId != null 
          ? await siteRepository.getSiteById(area.siteId!) 
          : null;

      onProgress?.call(0.3);

      // Prepare CSV content
      final csvContent = StringBuffer();
      
      // Add header information
      csvContent.writeln('SafeStride Inspection Report');
      csvContent.writeln('Generated on: ${DateTime.now().toIso8601String()}');
      csvContent.writeln('');
      
      // Session information
      csvContent.writeln('SESSION INFORMATION');
      csvContent.writeln('Session ID,${_escapeCsvValue(session.id)}');
      csvContent.writeln('Session Type,${_escapeCsvValue(session.type.name)}');
      csvContent.writeln('Status,${_escapeCsvValue(session.status.name)}');
      csvContent.writeln('Created At,${_formatDateTime(session.createdAt)}');
      if (session.completedAt != null) {
        csvContent.writeln('Completed At,${_formatDateTime(session.completedAt!)}');
      }
      csvContent.writeln('Site,${_escapeCsvValue(site?.name ?? 'Unknown')}');
      csvContent.writeln('Area,${_escapeCsvValue(area?.name ?? 'Unknown')}');
      csvContent.writeln('');

      onProgress?.call(0.4);

      // Export findings if requested
      if (includeFindings) {
        await _exportFindings(csvContent, sessionId);
        onProgress?.call(0.7);
      }

      // Export checklist if requested
      if (includeChecklist) {
        await _exportChecklist(csvContent, sessionId);
        onProgress?.call(0.9);
      }

      // Save to file
      final filePath = await _saveCsvToFile(csvContent.toString(), sessionId);
      onProgress?.call(1.0);

      return filePath;
    } catch (e) {
      throw Exception('Failed to export CSV: $e');
    }
  }

  /// Export findings data to CSV section
  Future<void> _exportFindings(StringBuffer csvContent, String sessionId) async {
    final findingRepository = _repositoryFactory.getFindingRepository();
    final findings = await findingRepository.getFindingsBySession(sessionId);

    csvContent.writeln('FINDINGS');
    if (findings.isEmpty) {
      csvContent.writeln('No findings reported for this session.');
      csvContent.writeln('');
      return;
    }

    // Findings header
    csvContent.writeln('ID,Description,Severity,Category,Status,Author ID,Created At,Photo Path');
    
    // Findings data
    for (final finding in findings) {
      csvContent.writeln([
        _escapeCsvValue(finding.id),
        _escapeCsvValue(finding.description),
        _escapeCsvValue(finding.severity.name),
        _escapeCsvValue(finding.category.name),
        _escapeCsvValue(finding.status.name),
        _escapeCsvValue(finding.authorId),
        _formatDateTime(finding.createdAt),
        _escapeCsvValue(finding.photoPath ?? ''),
      ].join(','));
    }
    csvContent.writeln('');
  }

  /// Export checklist data to CSV section
  Future<void> _exportChecklist(StringBuffer csvContent, String sessionId) async {
    final checklistRepository = _repositoryFactory.getChecklistRepository();
    final checklistItems = await checklistRepository.getChecklistBySession(sessionId);

    csvContent.writeln('CHECKLIST');
    if (checklistItems.isEmpty) {
      csvContent.writeln('No checklist data available for this session.');
      csvContent.writeln('');
      return;
    }

    // Checklist header
    csvContent.writeln('ID,Title,Description,Status,Notes,Completed At,Photo Path');
    
    // Checklist data
    for (final item in checklistItems) {
      csvContent.writeln([
        _escapeCsvValue(item.id),
        _escapeCsvValue(item.title),
        _escapeCsvValue(item.description),
        _escapeCsvValue(item.status.name),
        _escapeCsvValue(item.notes ?? ''),
        item.completedAt != null ? _formatDateTime(item.completedAt!) : '',
        _escapeCsvValue(item.photoPath ?? ''),
      ].join(','));
    }
    csvContent.writeln('');
  }

  /// Save CSV content to file
  Future<String> _saveCsvToFile(String csvContent, String sessionId) async {
    final directory = await getApplicationDocumentsDirectory();
    final exportsDir = Directory(path.join(directory.path, 'exports'));
    
    // Create exports directory if it doesn't exist
    if (!await exportsDir.exists()) {
      await exportsDir.create(recursive: true);
    }
    
    // Generate filename with timestamp
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = 'inspection_${sessionId}_$timestamp.csv';
    final filePath = path.join(exportsDir.path, fileName);
    
    // Write file
    final file = File(filePath);
    await file.writeAsString(csvContent, encoding: utf8);
    
    return filePath;
  }

  /// Share CSV file via email or other apps
  Future<void> shareCsvFile(String filePath, {String? subject}) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('CSV file not found');
    }

    final fileName = path.basename(filePath);
    final defaultSubject = 'SafeStride Inspection Report - $fileName';
    
    await Share.shareXFiles(
      [XFile(filePath)],
      subject: subject ?? defaultSubject,
      text: 'Please find attached the SafeStride inspection report.',
    );
  }

  /// Get list of exported CSV files
  Future<List<FileSystemEntity>> getExportedFiles() async {
    final directory = await getApplicationDocumentsDirectory();
    final exportsDir = Directory(path.join(directory.path, 'exports'));
    
    if (!await exportsDir.exists()) {
      return [];
    }
    
    final files = await exportsDir.list().toList();
    files.sort((a, b) {
      final aStat = a.statSync();
      final bStat = b.statSync();
      return bStat.modified.compareTo(aStat.modified); // Most recent first
    });
    
    return files;
  }

  /// Delete exported CSV file
  Future<bool> deleteExportedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }

  /// Get file size in bytes
  Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }

  /// Format file size for display
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Escape CSV values to handle commas, quotes, and newlines
  String _escapeCsvValue(String value) {
    if (value.contains(',') || value.contains('"') || value.contains('\n')) {
      return '"${value.replaceAll('"', '""')}"';
    }
    return value;
  }

  /// Format DateTime for CSV export
  String _formatDateTime(DateTime dateTime) {
    return dateTime.toIso8601String();
  }

  /// Export multiple sessions to a single CSV file
  Future<String> exportMultipleSessionsToCsv({
    required List<String> sessionIds,
    Function(double)? onProgress,
  }) async {
    try {
      final csvContent = StringBuffer();
      
      // Add header information
      csvContent.writeln('SafeStride Multiple Sessions Report');
      csvContent.writeln('Generated on: ${DateTime.now().toIso8601String()}');
      csvContent.writeln('Sessions Count: ${sessionIds.length}');
      csvContent.writeln('');

      for (int i = 0; i < sessionIds.length; i++) {
        final sessionId = sessionIds[i];
        onProgress?.call((i / sessionIds.length) * 0.9);
        
        csvContent.writeln('SESSION ${i + 1}');
        csvContent.writeln('Session ID: $sessionId');
        csvContent.writeln('');
        
        // Export individual session data
        await _exportFindings(csvContent, sessionId);
        await _exportChecklist(csvContent, sessionId);
        
        csvContent.writeln('---');
        csvContent.writeln('');
      }

      // Save to file
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = await _saveCsvToFile(
        csvContent.toString(), 
        'multiple_sessions_$timestamp'
      );
      
      onProgress?.call(1.0);
      return filePath;
    } catch (e) {
      throw Exception('Failed to export multiple sessions: $e');
    }
  }
}
