# Frontend Developer Standards

## Critical Coding Rules
1. **Offline-First:** All core features must work without network connectivity
2. **Accessibility:** Minimum 48dp touch targets, high contrast colors, semantic labels
3. **Performance:** 60fps target, lazy loading for lists, image optimization
4. **Error Handling:** Graceful degradation with user-friendly error messages
5. **State Management:** Use Provider pattern consistently, avoid setState in complex widgets
6. **Testing:** Minimum 80% test coverage for business logic widgets
7. **Security:** Never store sensitive data in plain text, use secure storage
8. **Responsive Design:** Support various screen sizes and orientations

## Quick Reference
- **File Naming:** snake_case for files, PascalCase for classes
- **Widget Structure:** StatelessWidget preferred, StatefulWidget only when necessary
- **Error Boundaries:** Wrap async operations in try-catch blocks
- **Loading States:** Always show loading indicators for async operations
- **Navigation:** Use go_router for declarative navigation
- **Theming:** Use theme data consistently, avoid hardcoded colors/sizes

---

*This frontend architecture document provides the detailed implementation guidance for SafeStride's Flutter mobile application, ensuring consistency with the overall system architecture while addressing mobile-specific concerns.*