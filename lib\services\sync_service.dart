import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../repositories/user_repository.dart';
import '../repositories/session_repository.dart';
import '../repositories/finding_repository.dart';
import '../repositories/site_repository.dart';
import '../repositories/area_repository.dart';
import '../repositories/checklist_repository.dart';
import 'database_service.dart';
import 'firestore_service.dart';

/// Service for synchronizing data between local SQLite and Firestore
class SyncService {
  final UserRepository _localUserRepository;
  final SessionRepository _localSessionRepository;
  final FindingRepository _localFindingRepository;
  final SiteRepository _localSiteRepository;
  final AreaRepository _localAreaRepository;
  final ChecklistRepository _localChecklistRepository;

  static const String _syncQueueTable = 'sync_queue';
  static const int _maxRetryCount = 3;

  SyncService(
    this._localUserRepository,
    this._localSessionRepository,
    this._localFindingRepository,
    this._localSiteRepository,
    this._localAreaRepository,
    this._localChecklistRepository,
  );

  /// Check if device has internet connectivity
  Future<bool> hasConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// Add operation to sync queue
  Future<void> addToSyncQueue({
    required String operationType,
    required String tableName,
    required String recordId,
    required Map<String, dynamic> data,
  }) async {
    final db = await DatabaseService.database;
    await db.insert(_syncQueueTable, {
      'operation_type': operationType,
      'table_name': tableName,
      'record_id': recordId,
      'data': jsonEncode(data),
      'created_at': DateTime.now().millisecondsSinceEpoch,
      'retry_count': 0,
    });
  }

  /// Sync all pending operations
  Future<void> syncAll() async {
    if (!await hasConnectivity()) {
      return;
    }

    try {
      await _syncUsers();
      await _syncSessions();
      await _syncFindings();
      await _processSyncQueue();
    } catch (e) {
      throw Exception('Sync failed: $e');
    }
  }

  /// Sync users
  Future<void> _syncUsers() async {
    final unsyncedUsers = await _localUserRepository.getUnsyncedUsers();

    for (final user in unsyncedUsers) {
      try {
        await FirestoreService.createUser(user);
        await _localUserRepository.markUserAsSynced(user.id);
      } catch (e) {
        // Add to sync queue for retry
        await addToSyncQueue(
          operationType: 'create',
          tableName: 'users',
          recordId: user.id,
          data: user.toMap(),
        );
      }
    }
  }

  /// Sync sessions
  Future<void> _syncSessions() async {
    final unsyncedSessions =
        await _localSessionRepository.getUnsyncedSessions();

    for (final session in unsyncedSessions) {
      try {
        await FirestoreService.createSession(session);
        await _localSessionRepository.markSessionAsSynced(session.id);
      } catch (e) {
        // Add to sync queue for retry
        await addToSyncQueue(
          operationType: 'create',
          tableName: 'sessions',
          recordId: session.id,
          data: session.toMap(),
        );
      }
    }
  }

  /// Sync findings
  Future<void> _syncFindings() async {
    final unsyncedFindings =
        await _localFindingRepository.getUnsyncedFindings();

    for (final finding in unsyncedFindings) {
      try {
        await FirestoreService.createFinding(finding.sessionId, finding);
        await _localFindingRepository.markFindingAsSynced(finding.id);
      } catch (e) {
        // Add to sync queue for retry
        await addToSyncQueue(
          operationType: 'create',
          tableName: 'findings',
          recordId: finding.id,
          data: finding.toMap(),
        );
      }
    }
  }

  /// Process sync queue
  Future<void> _processSyncQueue() async {
    final db = await DatabaseService.database;
    final queueItems = await db.query(
      _syncQueueTable,
      where: 'retry_count < ?',
      whereArgs: [_maxRetryCount],
      orderBy: 'created_at ASC',
    );

    final List<Map<String, dynamic>> batchOperations = [];

    for (final item in queueItems) {
      try {
        final operationType = item['operation_type'] as String;
        final tableName = item['table_name'] as String;
        final recordId = item['record_id'] as String;
        final data = jsonDecode(item['data'] as String) as Map<String, dynamic>;

        // Add to batch operation
        batchOperations.add({
          'type': operationType,
          'collection': tableName,
          'docId': recordId,
          'data': data,
        });

        // Remove from sync queue on success
        await db.delete(
          _syncQueueTable,
          where: 'id = ?',
          whereArgs: [item['id']],
        );
      } catch (e) {
        // Increment retry count
        await db.update(
          _syncQueueTable,
          {'retry_count': (item['retry_count'] as int) + 1},
          where: 'id = ?',
          whereArgs: [item['id']],
        );
      }
    }

    // Execute batch operations
    if (batchOperations.isNotEmpty) {
      try {
        await FirestoreService.batchWrite(batchOperations);
      } catch (e) {
        throw Exception('Batch sync failed: $e');
      }
    }
  }

  /// Clear sync queue
  Future<void> clearSyncQueue() async {
    final db = await DatabaseService.database;
    await db.delete(_syncQueueTable);
  }

  /// Get sync queue status
  Future<Map<String, int>> getSyncQueueStatus() async {
    final db = await DatabaseService.database;
    final result = await db.rawQuery('''
      SELECT operation_type, COUNT(*) as count 
      FROM $_syncQueueTable 
      GROUP BY operation_type
    ''');

    final Map<String, int> status = {};
    for (final row in result) {
      status[row['operation_type'] as String] = row['count'] as int;
    }
    return status;
  }

  /// Auto-sync when connectivity is restored
  void startAutoSync() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      if (result != ConnectivityResult.none) {
        syncAll().catchError((error) {
          // Log error but don't throw to avoid breaking the stream
          // TODO: Replace with proper logging framework
          // ignore: avoid_print
          print('Auto-sync failed: $error');
        });
      }
    });
  }
}
