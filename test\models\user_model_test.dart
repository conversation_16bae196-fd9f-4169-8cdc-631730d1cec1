import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/user_model.dart';

void main() {
  group('UserModel', () {
    late UserModel testUser;

    setUp(() {
      testUser = UserModel(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
        createdAt: DateTime(2024, 1, 1),
      );
    });

    test('should create UserModel with all properties', () {
      expect(testUser.id, 'test-user-id');
      expect(testUser.email, '<EMAIL>');
      expect(testUser.name, 'Test User');
      expect(testUser.role, UserRole.leader);
      expect(testUser.subscription, SubscriptionType.premium);
      expect(testUser.createdAt, DateTime(2024, 1, 1));
    });

    test('should convert UserModel to map correctly', () {
      final map = testUser.toMap();

      expect(map['id'], 'test-user-id');
      expect(map['email'], '<EMAIL>');
      expect(map['name'], 'Test User');
      expect(map['role'], 'leader');
      expect(map['subscription'], 'premium');
      expect(map['created_at'], DateTime(2024, 1, 1).millisecondsSinceEpoch);
    });

    test('should create UserModel from map correctly', () {
      final map = {
        'id': 'test-user-id',
        'email': '<EMAIL>',
        'name': 'Test User',
        'role': 'leader',
        'subscription': 'premium',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final user = UserModel.fromMap(map);

      expect(user.id, 'test-user-id');
      expect(user.email, '<EMAIL>');
      expect(user.name, 'Test User');
      expect(user.role, UserRole.leader);
      expect(user.subscription, SubscriptionType.premium);
      expect(user.createdAt, DateTime(2024, 1, 1));
    });

    test('should handle invalid role gracefully', () {
      final map = {
        'id': 'test-user-id',
        'email': '<EMAIL>',
        'name': 'Test User',
        'role': 'invalid-role',
        'subscription': 'free',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final user = UserModel.fromMap(map);
      expect(user.role, UserRole.observer); // Should default to observer
    });

    test('should handle invalid subscription gracefully', () {
      final map = {
        'id': 'test-user-id',
        'email': '<EMAIL>',
        'name': 'Test User',
        'role': 'leader',
        'subscription': 'invalid-subscription',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final user = UserModel.fromMap(map);
      expect(user.subscription, SubscriptionType.free); // Should default to free
    });

    test('should create copy with updated fields', () {
      final updatedUser = testUser.copyWith(
        name: 'Updated Name',
        role: UserRole.observer,
      );

      expect(updatedUser.id, testUser.id);
      expect(updatedUser.email, testUser.email);
      expect(updatedUser.name, 'Updated Name');
      expect(updatedUser.role, UserRole.observer);
      expect(updatedUser.subscription, testUser.subscription);
      expect(updatedUser.createdAt, testUser.createdAt);
    });

    test('should implement equality correctly', () {
      final user1 = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
        createdAt: DateTime(2024, 1, 1),
      );

      final user2 = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
        createdAt: DateTime(2024, 1, 1),
      );

      final user3 = user1.copyWith(name: 'Different Name');

      expect(user1, equals(user2));
      expect(user1, isNot(equals(user3)));
    });

    test('should have consistent hashCode', () {
      final user1 = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
        createdAt: DateTime(2024, 1, 1),
      );

      final user2 = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.premium,
        createdAt: DateTime(2024, 1, 1),
      );

      expect(user1.hashCode, equals(user2.hashCode));
    });

    test('should have meaningful toString', () {
      final toString = testUser.toString();
      expect(toString, contains('UserModel'));
      expect(toString, contains('test-user-id'));
      expect(toString, contains('<EMAIL>'));
    });
  });
}
