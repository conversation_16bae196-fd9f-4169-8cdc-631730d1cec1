import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/user_model.dart';
import 'package:safestride/models/site_model.dart';
import 'package:safestride/models/area_model.dart';
import 'package:safestride/models/session_model.dart';
import 'package:safestride/models/finding_model.dart';
import 'package:safestride/models/checklist_item_model.dart';
import 'package:safestride/repositories/repository_factory.dart';
import 'package:safestride/services/database_service.dart';

void main() {
  group('End-to-End Solo Walkabout Workflow', () {
    late RepositoryFactory repositoryFactory;
    late UserModel testUser;

    setUpAll(() async {
      // Initialize database
      await DatabaseService.database;
      repositoryFactory = RepositoryFactory();
      
      testUser = UserModel(
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );
    });

    tearDown(() async {
      // Clean up after each test
      await DatabaseService.clearAllData();
    });

    test('Complete solo walkabout workflow should work end-to-end', () async {
      // Step 1: Create or get default site
      final siteRepository = repositoryFactory.getSiteRepository();
      final defaultSite = await siteRepository.getOrCreateDefaultSite(testUser.id);
      
      expect(defaultSite, isNotNull);
      expect(defaultSite.name, 'Site');
      expect(defaultSite.ownerId, testUser.id);

      // Step 2: Get areas for the site
      final areaRepository = repositoryFactory.getAreaRepository();
      final areas = await areaRepository.getAreasBySite(defaultSite.id);
      
      expect(areas, isNotEmpty);
      final testArea = areas.first;
      expect(testArea.name, 'Site');
      expect(testArea.siteId, defaultSite.id);

      // Step 3: Create a new inspection session
      final sessionRepository = repositoryFactory.getSessionRepository();
      final session = SessionModel(
        id: 'test-session-id',
        type: SessionType.inspection,
        leaderId: testUser.id,
        areaId: testArea.id,
        status: SessionStatus.active,
        inviteCode: '',
        createdAt: DateTime.now(),
      );

      await sessionRepository.createSession(session);
      
      // Verify session was created
      final retrievedSession = await sessionRepository.getSessionById(session.id);
      expect(retrievedSession, isNotNull);
      expect(retrievedSession!.status, SessionStatus.active);

      // Step 4: Complete checklist items
      final checklistRepository = repositoryFactory.getChecklistRepository();
      final checklistItems = DefaultChecklistItems.generateDefaultItems();
      
      // Mark some items as passed and some as failed
      final updatedItems = checklistItems.map((item) {
        if (checklistItems.indexOf(item) < 5) {
          return item.copyWith(
            status: ChecklistItemStatus.pass,
            completedAt: DateTime.now(),
            notes: 'Test notes for ${item.title}',
          );
        } else {
          return item.copyWith(
            status: ChecklistItemStatus.fail,
            completedAt: DateTime.now(),
            notes: 'Failed: ${item.title}',
          );
        }
      }).toList();

      await checklistRepository.saveChecklist(session.id, updatedItems);
      
      // Verify checklist was saved
      final savedChecklist = await checklistRepository.getChecklistBySession(session.id);
      expect(savedChecklist.length, 10);
      expect(savedChecklist.where((item) => item.isPassed).length, 5);
      expect(savedChecklist.where((item) => item.isFailed).length, 5);

      // Step 5: Create hazard findings
      final findingRepository = repositoryFactory.getFindingRepository();
      final hazard1 = FindingModel(
        id: 'hazard-1',
        sessionId: session.id,
        description: 'Broken safety equipment found',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: testUser.id,
        status: FindingStatus.open,
        createdAt: DateTime.now(),
      );

      final hazard2 = FindingModel(
        id: 'hazard-2',
        sessionId: session.id,
        description: 'Minor housekeeping issue',
        severity: FindingSeverity.low,
        category: FindingCategory.environmental,
        authorId: testUser.id,
        status: FindingStatus.open,
        createdAt: DateTime.now(),
      );

      await findingRepository.createFinding(hazard1);
      await findingRepository.createFinding(hazard2);
      
      // Verify findings were created
      final sessionFindings = await findingRepository.getFindingsBySession(session.id);
      expect(sessionFindings.length, 2);
      expect(sessionFindings.any((f) => f.severity == FindingSeverity.high), true);
      expect(sessionFindings.any((f) => f.severity == FindingSeverity.low), true);

      // Step 6: Complete the session
      final completedSession = session.copyWith(
        status: SessionStatus.completed,
        completedAt: DateTime.now(),
      );

      await sessionRepository.updateSession(completedSession);
      
      // Verify session was completed
      final finalSession = await sessionRepository.getSessionById(session.id);
      expect(finalSession!.status, SessionStatus.completed);
      expect(finalSession.completedAt, isNotNull);

      // Step 7: Verify all data is properly stored and retrievable
      
      // Check that we can get all sessions for the user
      final userSessions = await sessionRepository.getSessionsByLeader(testUser.id);
      expect(userSessions.length, 1);
      expect(userSessions.first.status, SessionStatus.completed);

      // Check that we can get all findings for the session
      final allFindings = await findingRepository.getFindingsBySession(session.id);
      expect(allFindings.length, 2);

      // Check that we can get checklist data
      final finalChecklist = await checklistRepository.getChecklistBySession(session.id);
      expect(finalChecklist.length, 10);
      
      // Verify checklist completion statistics
      final completedItems = finalChecklist.where((item) => item.isCompleted).length;
      expect(completedItems, 10); // All items should be completed

      // Step 8: Test data export readiness
      // Verify that all data needed for CSV export is available
      expect(finalSession.completedAt, isNotNull);
      expect(testArea.name, isNotEmpty);
      expect(defaultSite.name, isNotEmpty);
      expect(allFindings.isNotEmpty, true);
      expect(finalChecklist.isNotEmpty, true);

      print('✅ End-to-end solo walkabout workflow completed successfully!');
      print('📊 Session: ${finalSession.id}');
      print('📍 Area: ${testArea.name}');
      print('✅ Checklist items completed: $completedItems/10');
      print('⚠️  Hazards found: ${allFindings.length}');
      print('🏁 Session status: ${finalSession.status.name}');
    });

    test('Repository factory should provide all required repositories', () {
      expect(repositoryFactory.getUserRepository(), completes);
      expect(repositoryFactory.getSessionRepository(), isNotNull);
      expect(repositoryFactory.getFindingRepository(), isNotNull);
      expect(repositoryFactory.getSiteRepository(), isNotNull);
      expect(repositoryFactory.getAreaRepository(), isNotNull);
      expect(repositoryFactory.getChecklistRepository(), isNotNull);
    });

    test('Default site and area creation should work for new users', () async {
      final siteRepository = repositoryFactory.getSiteRepository();
      final areaRepository = repositoryFactory.getAreaRepository();
      
      // Create default site for new user
      final defaultSite = await siteRepository.getOrCreateDefaultSite('new-user-id');
      expect(defaultSite.ownerId, 'new-user-id');
      expect(defaultSite.name, 'Site');
      
      // Verify default area was created
      final areas = await areaRepository.getAreasBySite(defaultSite.id);
      expect(areas.length, 1);
      expect(areas.first.name, 'Site');
      expect(areas.first.siteId, defaultSite.id);
    });

    test('Session state transitions should work correctly', () async {
      final sessionRepository = repositoryFactory.getSessionRepository();
      
      // Create active session
      final session = SessionModel(
        id: 'state-test-session',
        type: SessionType.inspection,
        leaderId: testUser.id,
        areaId: 'test-area-id',
        status: SessionStatus.active,
        inviteCode: '',
        createdAt: DateTime.now(),
      );

      await sessionRepository.createSession(session);
      
      // Verify active session
      final activeSession = await sessionRepository.getSessionById(session.id);
      expect(activeSession!.status, SessionStatus.active);
      expect(activeSession.completedAt, isNull);
      
      // Complete session
      final completedSession = session.copyWith(
        status: SessionStatus.completed,
        completedAt: DateTime.now(),
      );
      
      await sessionRepository.updateSession(completedSession);
      
      // Verify completed session
      final finalSession = await sessionRepository.getSessionById(session.id);
      expect(finalSession!.status, SessionStatus.completed);
      expect(finalSession.completedAt, isNotNull);
    });
  });
}
