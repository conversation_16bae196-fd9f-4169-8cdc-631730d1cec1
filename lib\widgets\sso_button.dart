import 'package:flutter/material.dart';

/// Enum for SSO providers
enum SSOProvider {
  google,
}

/// Reusable SSO button widget with proper branding
class SSOButton extends StatelessWidget {
  final SSOProvider provider;
  final VoidCallback? onPressed;
  final bool isLoading;

  const SSOButton({
    super.key,
    required this.provider,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 56, // WCAG compliant minimum touch target
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: theme.colorScheme.outline,
            width: 1.5,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          backgroundColor: theme.colorScheme.surface,
        ),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildProviderIcon(),
                  const SizedBox(width: 12),
                  Text(
                    _getButtonText(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildProviderIcon() {
    switch (provider) {
      case SSOProvider.google:
        return Container(
          width: 24,
          height: 24,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: ClipOval(
            child: Image.asset(
              'assets/images/google_logo.png',
              width: 24,
              height: 24,
              errorBuilder: (context, error, stackTrace) {
                // Fallback to icon if image not found
                return const Icon(
                  Icons.account_circle,
                  size: 24,
                  color: Colors.red,
                );
              },
            ),
          ),
        );
    }
  }

  String _getButtonText() {
    switch (provider) {
      case SSOProvider.google:
        return 'Continue with Google';
    }
  }
}

/// Extension to get provider display names
extension SSOProviderExtension on SSOProvider {
  String get displayName {
    switch (this) {
      case SSOProvider.google:
        return 'Google';
    }
  }

  String get description {
    switch (this) {
      case SSOProvider.google:
        return 'Sign in with your Google account';
    }
  }

  Color get brandColor {
    switch (this) {
      case SSOProvider.google:
        return const Color(0xFF4285F4); // Google Blue
    }
  }
}
