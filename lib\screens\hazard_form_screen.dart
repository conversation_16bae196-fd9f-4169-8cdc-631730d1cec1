import 'dart:io';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/finding_model.dart';
import '../services/photo_service.dart';
import '../utils/app_theme.dart';

/// Screen for documenting safety hazards
class HazardFormScreen extends StatefulWidget {
  final String sessionId;
  final String areaName;
  final FindingModel? existingFinding;
  final Function(FindingModel) onHazardSaved;

  const HazardFormScreen({
    super.key,
    required this.sessionId,
    required this.areaName,
    this.existingFinding,
    required this.onHazardSaved,
  });

  @override
  State<HazardFormScreen> createState() => _HazardFormScreenState();
}

class _HazardFormScreenState extends State<HazardFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final PhotoService _photoService = PhotoService();
  
  FindingSeverity _selectedSeverity = FindingSeverity.low;
  FindingCategory _selectedCategory = FindingCategory.safety;
  String? _photoPath;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.existingFinding != null) {
      _descriptionController.text = widget.existingFinding!.description;
      _selectedSeverity = widget.existingFinding!.severity;
      _selectedCategory = widget.existingFinding!.category;
      _photoPath = widget.existingFinding!.photoPath;
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.existingFinding != null ? 'Edit Hazard' : 'Report Hazard'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (widget.existingFinding != null)
            IconButton(
              icon: const Icon(Icons.preview),
              onPressed: _showPreview,
              tooltip: 'Preview',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Area info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.location_on, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      'Area: ${widget.areaName}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Description field
            Text(
              'Hazard Description',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                hintText: 'Describe the safety hazard in detail...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              textCapitalization: TextCapitalization.sentences,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please provide a description of the hazard';
                }
                if (value.trim().length < 10) {
                  return 'Description must be at least 10 characters';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 24),
            
            // Severity selection
            Text(
              'Severity Level',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            _buildSeveritySelector(),
            
            const SizedBox(height: 24),
            
            // Category selection
            Text(
              'Category',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Card(
              child: DropdownButtonFormField<FindingCategory>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  border: InputBorder.none,
                ),
                items: FindingCategory.values.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(_getCategoryDisplayName(category)),
                  );
                }).toList(),
                onChanged: (category) {
                  if (category != null) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  }
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Photo section
            _buildPhotoSection(),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            if (widget.existingFinding != null) ...[
              Expanded(
                child: OutlinedButton(
                  onPressed: _showPreview,
                  child: const Text('Preview'),
                ),
              ),
              const SizedBox(width: 12),
            ],
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveHazard,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        widget.existingFinding != null ? 'Update Hazard' : 'Save Hazard',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSeveritySelector() {
    return Row(
      children: FindingSeverity.values.map((severity) {
        final isSelected = _selectedSeverity == severity;
        final color = _getSeverityColor(severity);
        
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 8),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedSeverity = severity;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? color : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? color : Colors.grey[300]!,
                    width: 2,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      _getSeverityIcon(severity),
                      color: isSelected ? Colors.white : color,
                      size: 24,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getSeverityDisplayName(severity),
                      style: TextStyle(
                        color: isSelected ? Colors.white : color,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPhotoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.camera_alt, size: 20),
            const SizedBox(width: 8),
            Text(
              'Photo Evidence',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (_photoPath == null)
              TextButton.icon(
                onPressed: _addPhoto,
                icon: const Icon(Icons.add_a_photo),
                label: const Text('Add Photo'),
              ),
          ],
        ),
        
        if (_photoPath != null) ...[
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(_photoPath!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.broken_image, color: Colors.grey, size: 48),
                          SizedBox(height: 8),
                          Text('Image not found', style: TextStyle(color: Colors.grey)),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              TextButton.icon(
                onPressed: _replacePhoto,
                icon: const Icon(Icons.edit),
                label: const Text('Replace'),
              ),
              TextButton.icon(
                onPressed: _removePhoto,
                icon: const Icon(Icons.delete, color: Colors.red),
                label: const Text('Remove', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ] else ...[
          const SizedBox(height: 12),
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.add_a_photo, color: Colors.grey[400], size: 48),
                const SizedBox(height: 8),
                Text(
                  'Tap to add photo evidence',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Color _getSeverityColor(FindingSeverity severity) {
    switch (severity) {
      case FindingSeverity.low:
        return Colors.green;
      case FindingSeverity.medium:
        return Colors.orange;
      case FindingSeverity.high:
        return Colors.red;
      case FindingSeverity.critical:
        return Colors.red[900]!;
    }
  }

  IconData _getSeverityIcon(FindingSeverity severity) {
    switch (severity) {
      case FindingSeverity.low:
        return Icons.info;
      case FindingSeverity.medium:
        return Icons.warning;
      case FindingSeverity.high:
        return Icons.error;
      case FindingSeverity.critical:
        return Icons.dangerous;
    }
  }

  String _getSeverityDisplayName(FindingSeverity severity) {
    switch (severity) {
      case FindingSeverity.low:
        return 'Low';
      case FindingSeverity.medium:
        return 'Medium';
      case FindingSeverity.high:
        return 'High';
      case FindingSeverity.critical:
        return 'Critical';
    }
  }

  String _getCategoryDisplayName(FindingCategory category) {
    switch (category) {
      case FindingCategory.safety:
        return 'Safety';
      case FindingCategory.environmental:
        return 'Environmental';
      case FindingCategory.quality:
        return 'Quality';
      case FindingCategory.security:
        return 'Security';
      case FindingCategory.operational:
        return 'Operational';
    }
  }

  Future<void> _addPhoto() async {
    final imagePath = await _photoService.showPhotoSourceDialog(context);
    if (imagePath != null) {
      setState(() {
        _photoPath = imagePath;
      });
    }
  }

  Future<void> _replacePhoto() async {
    final imagePath = await _photoService.showPhotoSourceDialog(context);
    if (imagePath != null) {
      // Delete old photo if it exists
      if (_photoPath != null) {
        await _photoService.deleteImage(_photoPath!);
      }
      
      setState(() {
        _photoPath = imagePath;
      });
    }
  }

  Future<void> _removePhoto() async {
    if (_photoPath != null) {
      await _photoService.deleteImage(_photoPath!);
      setState(() {
        _photoPath = null;
      });
    }
  }

  void _showPreview() {
    if (widget.existingFinding == null) return;
    
    showDialog(
      context: context,
      builder: (context) => _HazardPreviewDialog(finding: widget.existingFinding!),
    );
  }

  Future<void> _saveHazard() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final finding = widget.existingFinding?.copyWith(
        description: _descriptionController.text.trim(),
        severity: _selectedSeverity,
        category: _selectedCategory,
        photoPath: _photoPath,
      ) ?? FindingModel(
        id: const Uuid().v4(),
        sessionId: widget.sessionId,
        description: _descriptionController.text.trim(),
        severity: _selectedSeverity,
        category: _selectedCategory,
        photoPath: _photoPath,
        authorId: '', // Will be set by the calling screen
        status: FindingStatus.open,
        createdAt: DateTime.now(),
      );

      widget.onHazardSaved(finding);
      
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving hazard: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Dialog for previewing hazard details
class _HazardPreviewDialog extends StatelessWidget {
  final FindingModel finding;

  const _HazardPreviewDialog({required this.finding});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Hazard Preview'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Severity badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getSeverityColor(finding.severity),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _getSeverityDisplayName(finding.severity),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Description
            Text(
              'Description:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(finding.description),
            
            const SizedBox(height: 16),
            
            // Category
            Text(
              'Category:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(_getCategoryDisplayName(finding.category)),
            
            // Photo if available
            if (finding.photoPath != null) ...[
              const SizedBox(height: 16),
              Text(
                'Photo Evidence:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    File(finding.photoPath!),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Center(
                          child: Icon(Icons.broken_image, color: Colors.grey),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Color _getSeverityColor(FindingSeverity severity) {
    switch (severity) {
      case FindingSeverity.low:
        return Colors.green;
      case FindingSeverity.medium:
        return Colors.orange;
      case FindingSeverity.high:
        return Colors.red;
      case FindingSeverity.critical:
        return Colors.red[900]!;
    }
  }

  String _getSeverityDisplayName(FindingSeverity severity) {
    switch (severity) {
      case FindingSeverity.low:
        return 'Low';
      case FindingSeverity.medium:
        return 'Medium';
      case FindingSeverity.high:
        return 'High';
      case FindingSeverity.critical:
        return 'Critical';
    }
  }

  String _getCategoryDisplayName(FindingCategory category) {
    switch (category) {
      case FindingCategory.safety:
        return 'Safety';
      case FindingCategory.environmental:
        return 'Environmental';
      case FindingCategory.quality:
        return 'Quality';
      case FindingCategory.security:
        return 'Security';
      case FindingCategory.operational:
        return 'Operational';
    }
  }
}
