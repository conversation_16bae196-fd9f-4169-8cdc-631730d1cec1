import 'package:sqflite/sqflite.dart';
import '../models/session_model.dart';
import '../services/database_service.dart';
import 'session_repository.dart';

/// SQLite implementation of SessionRepository
class SqliteSessionRepository implements SessionRepository {
  static const String _tableName = 'sessions';

  @override
  Future<SessionModel?> getSessionById(String id) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SessionModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<void> createSession(SessionModel session) async {
    final db = await DatabaseService.database;
    await db.insert(
      _tableName,
      session.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<void> updateSession(SessionModel session) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      session.toMap(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  @override
  Future<void> deleteSession(String id) async {
    final db = await DatabaseService.database;
    await db.delete(
      _tableName,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  @override
  Future<List<SessionModel>> getSessionsByLeader(String leaderId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'leader_id = ?',
      whereArgs: [leaderId],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => SessionModel.fromMap(map)).toList();
  }

  @override
  Future<List<SessionModel>> getSessionsByArea(String areaId) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'area_id = ?',
      whereArgs: [areaId],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => SessionModel.fromMap(map)).toList();
  }

  @override
  Future<SessionModel?> getSessionByInviteCode(String inviteCode) async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'invite_code = ?',
      whereArgs: [inviteCode],
    );

    if (maps.isNotEmpty) {
      return SessionModel.fromMap(maps.first);
    }
    return null;
  }

  @override
  Future<List<SessionModel>> getActiveSessions() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'status = ?',
      whereArgs: ['active'],
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => SessionModel.fromMap(map)).toList();
  }

  @override
  Future<List<SessionModel>> getAllSessions() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      orderBy: 'created_at DESC',
    );
    return maps.map((map) => SessionModel.fromMap(map)).toList();
  }

  @override
  Future<List<SessionModel>> getUnsyncedSessions() async {
    final db = await DatabaseService.database;
    final maps = await db.query(
      _tableName,
      where: 'synced = ?',
      whereArgs: [0],
    );
    return maps.map((map) => SessionModel.fromMap(map)).toList();
  }

  @override
  Future<void> markSessionAsSynced(String id) async {
    final db = await DatabaseService.database;
    await db.update(
      _tableName,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
