import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import '../models/session_model.dart';
import '../repositories/repository_factory.dart';
import '../services/csv_export_service.dart';
import '../utils/app_theme.dart';

/// Screen for managing CSV exports and viewing export history
class ExportScreen extends StatefulWidget {
  const ExportScreen({super.key});

  @override
  State<ExportScreen> createState() => _ExportScreenState();
}

class _ExportScreenState extends State<ExportScreen> {
  final RepositoryFactory _repositoryFactory = RepositoryFactory();
  final CsvExportService _csvExportService = CsvExportService();
  
  List<SessionModel> _sessions = [];
  List<FileSystemEntity> _exportedFiles = [];
  Set<String> _selectedSessions = {};
  bool _isLoading = true;
  bool _isExporting = false;
  double _exportProgress = 0.0;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load completed sessions
      final sessionRepository = _repositoryFactory.getSessionRepository();
      final allSessions = await sessionRepository.getAllSessions();
      _sessions = allSessions.where((s) => s.status == SessionStatus.completed).toList();
      
      // Load exported files
      _exportedFiles = await _csvExportService.getExportedFiles();
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Export Data'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_selectedSessions.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.file_download),
              onPressed: _isExporting ? null : _exportSelectedSessions,
              tooltip: 'Export Selected',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _buildContent(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'Unknown error occurred',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Export progress indicator
        if (_isExporting)
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  'Exporting... ${(_exportProgress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: _exportProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                ),
              ],
            ),
          ),
        
        // Tab bar
        Expanded(
          child: DefaultTabController(
            length: 2,
            child: Column(
              children: [
                TabBar(
                  labelColor: AppTheme.primaryColor,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: AppTheme.primaryColor,
                  tabs: const [
                    Tab(text: 'Sessions', icon: Icon(Icons.list)),
                    Tab(text: 'Exported Files', icon: Icon(Icons.folder)),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildSessionsTab(),
                      _buildExportedFilesTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsTab() {
    if (_sessions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No completed sessions found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Complete some inspections to export data',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Selection controls
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                '${_selectedSessions.length} selected',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Spacer(),
              TextButton(
                onPressed: _selectedSessions.isEmpty ? null : _clearSelection,
                child: const Text('Clear'),
              ),
              TextButton(
                onPressed: _sessions.length == _selectedSessions.length ? null : _selectAll,
                child: const Text('Select All'),
              ),
            ],
          ),
        ),
        
        // Sessions list
        Expanded(
          child: ListView.builder(
            itemCount: _sessions.length,
            itemBuilder: (context, index) {
              final session = _sessions[index];
              final isSelected = _selectedSessions.contains(session.id);
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: CheckboxListTile(
                  value: isSelected,
                  onChanged: (selected) => _toggleSessionSelection(session.id, selected ?? false),
                  title: Text('Session ${session.id.substring(0, 8)}...'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Completed: ${_formatDateTime(session.completedAt!)}'),
                      Text('Type: ${session.type.name}'),
                    ],
                  ),
                  secondary: IconButton(
                    icon: const Icon(Icons.file_download),
                    onPressed: _isExporting ? null : () => _exportSingleSession(session.id),
                    tooltip: 'Export This Session',
                  ),
                  activeColor: AppTheme.primaryColor,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildExportedFilesTab() {
    if (_exportedFiles.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.folder_open, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No exported files found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Export some sessions to see files here',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _exportedFiles.length,
      itemBuilder: (context, index) {
        final file = _exportedFiles[index];
        final fileName = path.basename(file.path);
        final fileStat = file.statSync();
        
        return Card(
          child: ListTile(
            leading: const Icon(Icons.insert_drive_file, color: Colors.green),
            title: Text(fileName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Size: ${_csvExportService.formatFileSize(fileStat.size)}'),
                Text('Modified: ${_formatDateTime(fileStat.modified)}'),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(Icons.share, size: 20),
                      SizedBox(width: 8),
                      Text('Share'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 20, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'share') {
                  _shareFile(file.path);
                } else if (value == 'delete') {
                  _deleteFile(file.path);
                }
              },
            ),
          ),
        );
      },
    );
  }

  void _toggleSessionSelection(String sessionId, bool selected) {
    setState(() {
      if (selected) {
        _selectedSessions.add(sessionId);
      } else {
        _selectedSessions.remove(sessionId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      _selectedSessions = _sessions.map((s) => s.id).toSet();
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedSessions.clear();
    });
  }

  Future<void> _exportSingleSession(String sessionId) async {
    setState(() {
      _isExporting = true;
      _exportProgress = 0.0;
    });

    try {
      final filePath = await _csvExportService.exportSessionToCsv(
        sessionId: sessionId,
        onProgress: (progress) {
          setState(() {
            _exportProgress = progress;
          });
        },
      );

      setState(() {
        _isExporting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export completed: ${path.basename(filePath)}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Share',
              onPressed: () => _shareFile(filePath),
            ),
          ),
        );
      }

      await _loadData(); // Refresh the exported files list
    } catch (e) {
      setState(() {
        _isExporting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportSelectedSessions() async {
    if (_selectedSessions.isEmpty) return;

    setState(() {
      _isExporting = true;
      _exportProgress = 0.0;
    });

    try {
      final filePath = await _csvExportService.exportMultipleSessionsToCsv(
        sessionIds: _selectedSessions.toList(),
        onProgress: (progress) {
          setState(() {
            _exportProgress = progress;
          });
        },
      );

      setState(() {
        _isExporting = false;
        _selectedSessions.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export completed: ${path.basename(filePath)}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Share',
              onPressed: () => _shareFile(filePath),
            ),
          ),
        );
      }

      await _loadData(); // Refresh the exported files list
    } catch (e) {
      setState(() {
        _isExporting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareFile(String filePath) async {
    try {
      await _csvExportService.shareCsvFile(filePath);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteFile(String filePath) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete File'),
        content: Text('Are you sure you want to delete ${path.basename(filePath)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _csvExportService.deleteExportedFile(filePath);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'File deleted successfully' : 'Failed to delete file'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
      if (success) {
        await _loadData(); // Refresh the list
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
