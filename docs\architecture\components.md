# Components

### Mobile Application (Flutter)
**Responsibility:** Primary user interface and offline functionality

**Key Modules:**
- Authentication Module (Firebase Auth integration)
- Role Management (Leader/Observer workflows)
- Inspection Module (checklist, hazard documentation)
- Collaboration Module (QR codes, real-time sync)
- AI Module (TensorFlow Lite hazard detection)
- Offline Storage (SQLite repository layer)
- Sync Engine (Firestore synchronization)

**Interfaces:**
- Firebase Authentication API
- Firestore Database API
- Device camera and storage APIs
- Local SQLite database

### Firebase Backend
**Responsibility:** Serverless backend services and data management

**Key Services:**
- Firebase Authentication (user management, SSO)
- Cloud Firestore (real-time database, offline sync)
- Cloud Storage (photo/document storage)
- Cloud Functions (business logic, data processing)
- Firebase Analytics (usage tracking)

**Security Rules:**
- Role-based access control
- Data isolation between organizations
- Read/write permissions based on user roles

### AI/ML Pipeline
**Responsibility:** On-device hazard detection and categorization

**Components:**
- MobileNetV2 model (<4MB) for image classification
- MiniLM model (<5MB) for text similarity/duplicate detection
- Image preprocessing pipeline
- Confidence scoring and fallback mechanisms

**Interfaces:**
- TensorFlow Lite runtime
- Device camera input
- Local model storage

