import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/screens/welcome_screen.dart';
import 'package:safestride/widgets/health_check_widget.dart';
import 'package:safestride/utils/app_theme.dart';
import 'package:safestride/constants/app_constants.dart';

void main() {
  group('App Integration Tests', () {
    testWidgets('Welcome screen displays all required elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      // Verify main title and subtitle
      expect(find.text('Welcome to SafeStride'), findsOneWidget);
      expect(find.text('Industrial Safety Inspection Platform'), findsOneWidget);
      
      // Verify action buttons
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Show Health Check'), findsOneWidget);
      
      // Verify version info
      expect(find.text('Version 1.0.0'), findsOneWidget);
      expect(find.text('Not signed in'), findsOneWidget);
    });

    testWidgets('Health check widget can be toggled', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      // Initially health check should not be visible
      expect(find.byType(HealthCheckWidget), findsNothing);
      
      // Tap show health check button
      await tester.tap(find.text('Show Health Check'));
      await tester.pump();
      
      // Health check widget should now be visible
      expect(find.byType(HealthCheckWidget), findsOneWidget);
      expect(find.text('System Health Check'), findsOneWidget);
      
      // Button text should change
      expect(find.text('Hide Health Check'), findsOneWidget);
      
      // Tap hide health check button
      await tester.tap(find.text('Hide Health Check'));
      await tester.pump();
      
      // Health check widget should be hidden again
      expect(find.byType(HealthCheckWidget), findsNothing);
    });

    testWidgets('Sign in dialog appears when sign in button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      // Tap sign in button
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Dialog should appear
      expect(find.text('Sign In'), findsNWidgets(2)); // Button + dialog title
      expect(find.text('Login functionality will be implemented in the next phase.'), findsOneWidget);
      expect(find.text('OK'), findsOneWidget);
      
      // Close dialog
      await tester.tap(find.text('OK'));
      await tester.pump();
      
      // Dialog should be closed
      expect(find.text('Login functionality will be implemented in the next phase.'), findsNothing);
    });

    testWidgets('Create account dialog appears when create account button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      // Tap create account button
      await tester.tap(find.text('Create Account'));
      await tester.pump();

      // Dialog should appear
      expect(find.text('Create Account'), findsNWidgets(2)); // Button + dialog title
      expect(find.text('Registration functionality will be implemented in the next phase.'), findsOneWidget);
      expect(find.text('OK'), findsOneWidget);
      
      // Close dialog
      await tester.tap(find.text('OK'));
      await tester.pump();
      
      // Dialog should be closed
      expect(find.text('Registration functionality will be implemented in the next phase.'), findsNothing);
    });

    testWidgets('App theme is applied correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      // Find the app logo container
      final logoContainer = find.byType(Container).first;
      final containerWidget = tester.widget<Container>(logoContainer);
      final decoration = containerWidget.decoration as BoxDecoration;
      
      // Verify theme colors are applied
      expect(decoration.color, AppTheme.primaryColor);
      expect(decoration.borderRadius, BorderRadius.circular(AppTheme.radiusXLarge));
    });

    testWidgets('Health check widget displays system status', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: HealthCheckWidget(),
          ),
        ),
      );

      // Wait for health check to complete
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Verify health check title
      expect(find.text('System Health Check'), findsOneWidget);
      
      // Verify refresh button
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      
      // Health check items should be present (though they may fail in test environment)
      // We just verify the structure exists
      expect(find.byType(HealthCheckWidget), findsOneWidget);
    });

    testWidgets('App constants are properly defined', (WidgetTester tester) async {
      // Test that constants are accessible and have expected values
      expect(AppConstants.appName, 'SafeStride');
      expect(AppConstants.appVersion, '1.0.0');
      expect(AppConstants.appDescription, 'Industrial Safety Inspection Platform');
      expect(AppConstants.databaseName, 'safestride.db');
      expect(AppConstants.databaseVersion, 1);
      expect(AppConstants.minPasswordLength, 8);
      expect(AppConstants.maxDescriptionLength, 500);
    });

    testWidgets('App theme constants are properly defined', (WidgetTester tester) async {
      // Test that theme constants are accessible
      expect(AppTheme.primaryColor, const Color(0xFF1976D2));
      expect(AppTheme.secondaryColor, const Color(0xFF388E3C));
      expect(AppTheme.spacingMedium, 16.0);
      expect(AppTheme.spacingLarge, 24.0);
      expect(AppTheme.radiusMedium, 8.0);
      expect(AppTheme.radiusLarge, 12.0);
    });

    testWidgets('Welcome screen is responsive', (WidgetTester tester) async {
      // Test with different screen sizes
      await tester.binding.setSurfaceSize(const Size(400, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const WelcomeScreen(),
        ),
      );

      expect(find.text('Welcome to SafeStride'), findsOneWidget);
      
      // Test with smaller screen
      await tester.binding.setSurfaceSize(const Size(300, 600));
      await tester.pump();
      
      expect(find.text('Welcome to SafeStride'), findsOneWidget);
      
      // Reset to default size
      await tester.binding.setSurfaceSize(null);
    });
  });
}
