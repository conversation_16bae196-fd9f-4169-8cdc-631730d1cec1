import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/site_model.dart';

void main() {
  group('SiteModel', () {
    test('should create SiteModel with required fields', () {
      final site = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(site.id, 'test-id');
      expect(site.name, 'Test Site');
      expect(site.address, 'Test Address');
      expect(site.ownerId, isNull);
      expect(site.description, isNull);
      expect(site.latitude, isNull);
      expect(site.longitude, isNull);
      expect(site.createdAt, DateTime(2024, 1, 1));
    });

    test('should create SiteModel with all fields', () {
      final site = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        ownerId: 'owner-id',
        description: 'Test description',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime(2024, 1, 1),
      );

      expect(site.id, 'test-id');
      expect(site.name, 'Test Site');
      expect(site.address, 'Test Address');
      expect(site.ownerId, 'owner-id');
      expect(site.description, 'Test description');
      expect(site.latitude, 40.7128);
      expect(site.longitude, -74.0060);
      expect(site.createdAt, DateTime(2024, 1, 1));
    });

    test('should convert to map correctly', () {
      final site = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        ownerId: 'owner-id',
        description: 'Test description',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime(2024, 1, 1),
      );

      final map = site.toMap();

      expect(map['id'], 'test-id');
      expect(map['name'], 'Test Site');
      expect(map['address'], 'Test Address');
      expect(map['owner_id'], 'owner-id');
      expect(map['description'], 'Test description');
      expect(map['latitude'], 40.7128);
      expect(map['longitude'], -74.0060);
      expect(map['created_at'], DateTime(2024, 1, 1).millisecondsSinceEpoch);
    });

    test('should create from map correctly', () {
      final map = {
        'id': 'test-id',
        'name': 'Test Site',
        'address': 'Test Address',
        'owner_id': 'owner-id',
        'description': 'Test description',
        'latitude': 40.7128,
        'longitude': -74.0060,
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final site = SiteModel.fromMap(map);

      expect(site.id, 'test-id');
      expect(site.name, 'Test Site');
      expect(site.address, 'Test Address');
      expect(site.ownerId, 'owner-id');
      expect(site.description, 'Test description');
      expect(site.latitude, 40.7128);
      expect(site.longitude, -74.0060);
      expect(site.createdAt, DateTime(2024, 1, 1));
    });

    test('should create from map with string date', () {
      final map = {
        'id': 'test-id',
        'name': 'Test Site',
        'address': 'Test Address',
        'owner_id': null,
        'description': null,
        'latitude': null,
        'longitude': null,
        'created_at': '2024-01-01T00:00:00.000Z',
      };

      final site = SiteModel.fromMap(map);

      expect(site.id, 'test-id');
      expect(site.name, 'Test Site');
      expect(site.address, 'Test Address');
      expect(site.ownerId, isNull);
      expect(site.description, isNull);
      expect(site.latitude, isNull);
      expect(site.longitude, isNull);
      expect(site.createdAt, DateTime.parse('2024-01-01T00:00:00.000Z'));
    });

    test('should copy with updated fields', () {
      final original = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        createdAt: DateTime(2024, 1, 1),
      );

      final updated = original.copyWith(
        name: 'Updated Site',
        ownerId: 'new-owner',
        description: 'New description',
        latitude: 40.7128,
        longitude: -74.0060,
      );

      expect(updated.id, 'test-id');
      expect(updated.name, 'Updated Site');
      expect(updated.address, 'Test Address');
      expect(updated.ownerId, 'new-owner');
      expect(updated.description, 'New description');
      expect(updated.latitude, 40.7128);
      expect(updated.longitude, -74.0060);
      expect(updated.createdAt, DateTime(2024, 1, 1));
    });

    test('should implement equality correctly', () {
      final site1 = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        createdAt: DateTime(2024, 1, 1),
      );

      final site2 = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        createdAt: DateTime(2024, 1, 1),
      );

      final site3 = SiteModel(
        id: 'different-id',
        name: 'Test Site',
        address: 'Test Address',
        createdAt: DateTime(2024, 1, 1),
      );

      expect(site1, equals(site2));
      expect(site1, isNot(equals(site3)));
      expect(site1.hashCode, equals(site2.hashCode));
      expect(site1.hashCode, isNot(equals(site3.hashCode)));
    });

    test('should convert to string correctly', () {
      final site = SiteModel(
        id: 'test-id',
        name: 'Test Site',
        address: 'Test Address',
        ownerId: 'owner-id',
        description: 'Test description',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime(2024, 1, 1),
      );

      final string = site.toString();

      expect(string, contains('test-id'));
      expect(string, contains('Test Site'));
      expect(string, contains('Test Address'));
      expect(string, contains('owner-id'));
      expect(string, contains('Test description'));
      expect(string, contains('40.7128'));
      expect(string, contains('-74.006'));
    });
  });
}
