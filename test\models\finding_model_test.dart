import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/finding_model.dart';

void main() {
  group('FindingModel', () {
    late FindingModel testFinding;

    setUp(() {
      testFinding = FindingModel(
        id: 'test-finding-id',
        sessionId: 'session-id',
        description: 'Test finding description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        photoPath: '/path/to/photo.jpg',
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );
    });

    test('should create FindingModel with all properties', () {
      expect(testFinding.id, 'test-finding-id');
      expect(testFinding.sessionId, 'session-id');
      expect(testFinding.description, 'Test finding description');
      expect(testFinding.severity, FindingSeverity.high);
      expect(testFinding.category, FindingCategory.safety);
      expect(testFinding.photoPath, '/path/to/photo.jpg');
      expect(testFinding.authorId, 'author-id');
      expect(testFinding.status, FindingStatus.open);
      expect(testFinding.createdAt, DateTime(2024, 1, 1));
    });

    test('should create FindingModel without photo path', () {
      final finding = FindingModel(
        id: 'test-finding-id',
        sessionId: 'session-id',
        description: 'Test finding description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );

      expect(finding.photoPath, isNull);
    });

    test('should convert FindingModel to map correctly', () {
      final map = testFinding.toMap();

      expect(map['id'], 'test-finding-id');
      expect(map['session_id'], 'session-id');
      expect(map['description'], 'Test finding description');
      expect(map['severity'], 'high');
      expect(map['category'], 'safety');
      expect(map['photo_path'], '/path/to/photo.jpg');
      expect(map['author_id'], 'author-id');
      expect(map['status'], 'open');
      expect(map['created_at'], DateTime(2024, 1, 1).millisecondsSinceEpoch);
    });

    test('should create FindingModel from map correctly', () {
      final map = {
        'id': 'test-finding-id',
        'session_id': 'session-id',
        'description': 'Test finding description',
        'severity': 'high',
        'category': 'safety',
        'photo_path': '/path/to/photo.jpg',
        'author_id': 'author-id',
        'status': 'open',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final finding = FindingModel.fromMap(map);

      expect(finding.id, 'test-finding-id');
      expect(finding.sessionId, 'session-id');
      expect(finding.description, 'Test finding description');
      expect(finding.severity, FindingSeverity.high);
      expect(finding.category, FindingCategory.safety);
      expect(finding.photoPath, '/path/to/photo.jpg');
      expect(finding.authorId, 'author-id');
      expect(finding.status, FindingStatus.open);
      expect(finding.createdAt, DateTime(2024, 1, 1));
    });

    test('should handle invalid severity gracefully', () {
      final map = {
        'id': 'test-finding-id',
        'session_id': 'session-id',
        'description': 'Test finding description',
        'severity': 'invalid-severity',
        'category': 'safety',
        'author_id': 'author-id',
        'status': 'open',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final finding = FindingModel.fromMap(map);
      expect(finding.severity, FindingSeverity.low); // Should default to low
    });

    test('should handle invalid category gracefully', () {
      final map = {
        'id': 'test-finding-id',
        'session_id': 'session-id',
        'description': 'Test finding description',
        'severity': 'high',
        'category': 'invalid-category',
        'author_id': 'author-id',
        'status': 'open',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final finding = FindingModel.fromMap(map);
      expect(finding.category, FindingCategory.safety); // Should default to safety
    });

    test('should handle invalid status gracefully', () {
      final map = {
        'id': 'test-finding-id',
        'session_id': 'session-id',
        'description': 'Test finding description',
        'severity': 'high',
        'category': 'safety',
        'author_id': 'author-id',
        'status': 'invalid-status',
        'created_at': DateTime(2024, 1, 1).millisecondsSinceEpoch,
      };

      final finding = FindingModel.fromMap(map);
      expect(finding.status, FindingStatus.open); // Should default to open
    });

    test('should create copy with updated fields', () {
      final updatedFinding = testFinding.copyWith(
        status: FindingStatus.resolved,
        severity: FindingSeverity.critical,
      );

      expect(updatedFinding.id, testFinding.id);
      expect(updatedFinding.sessionId, testFinding.sessionId);
      expect(updatedFinding.description, testFinding.description);
      expect(updatedFinding.severity, FindingSeverity.critical);
      expect(updatedFinding.category, testFinding.category);
      expect(updatedFinding.photoPath, testFinding.photoPath);
      expect(updatedFinding.authorId, testFinding.authorId);
      expect(updatedFinding.status, FindingStatus.resolved);
      expect(updatedFinding.createdAt, testFinding.createdAt);
    });

    test('should implement equality correctly', () {
      final finding1 = FindingModel(
        id: 'test-id',
        sessionId: 'session-id',
        description: 'Test description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );

      final finding2 = FindingModel(
        id: 'test-id',
        sessionId: 'session-id',
        description: 'Test description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );

      final finding3 = finding1.copyWith(status: FindingStatus.resolved);

      expect(finding1, equals(finding2));
      expect(finding1, isNot(equals(finding3)));
    });

    test('should have consistent hashCode', () {
      final finding1 = FindingModel(
        id: 'test-id',
        sessionId: 'session-id',
        description: 'Test description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );

      final finding2 = FindingModel(
        id: 'test-id',
        sessionId: 'session-id',
        description: 'Test description',
        severity: FindingSeverity.high,
        category: FindingCategory.safety,
        authorId: 'author-id',
        status: FindingStatus.open,
        createdAt: DateTime(2024, 1, 1),
      );

      expect(finding1.hashCode, equals(finding2.hashCode));
    });

    test('should have meaningful toString', () {
      final toString = testFinding.toString();
      expect(toString, contains('FindingModel'));
      expect(toString, contains('test-finding-id'));
      expect(toString, contains('high'));
    });
  });
}
