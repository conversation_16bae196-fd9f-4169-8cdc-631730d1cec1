# User Responsibilities

## Account Creation & Service Setup

**User Actions Required:**

1. **Google Workspace/Gmail Account**
   - Create or provide existing Google account for Drive integration
   - Enable Google Drive API access in Google Cloud Console
   - Generate service account credentials or OAuth client credentials

2. **Dropbox Business Account** (Premium Users)
   - Create Dropbox Business account if advanced sharing required
   - Generate Dropbox API app credentials
   - Configure app permissions for file upload/sharing

3. **Firebase Project Setup**
   - Create Firebase project in Google Cloud Console
   - Enable required services (Auth, Firestore, Storage, Analytics)
   - Download configuration files (google-services.json, GoogleService-Info.plist)

## Credential Provision

**User Must Provide:**

- API keys and client secrets
- Service account JSON files
- OAuth client configuration
- Firebase configuration files
- Tenant/organization IDs where applicable
