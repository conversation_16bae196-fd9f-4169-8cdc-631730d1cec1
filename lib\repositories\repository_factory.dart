import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/user_model.dart';
import 'user_repository.dart';
import 'session_repository.dart';
import 'finding_repository.dart';
import 'site_repository.dart';
import 'area_repository.dart';
import 'sqlite_user_repository.dart';
import 'sqlite_session_repository.dart';
import 'sqlite_finding_repository.dart';
import 'sqlite_site_repository.dart';
import 'sqlite_area_repository.dart';
import 'firebase_user_repository.dart';

/// Factory class for creating repository instances based on connectivity
class RepositoryFactory {
  static final RepositoryFactory _instance = RepositoryFactory._internal();
  factory RepositoryFactory() => _instance;
  RepositoryFactory._internal();

  // Local repository instances
  final UserRepository _localUserRepository = SqliteUserRepository();
  final SessionRepository _localSessionRepository = SqliteSessionRepository();
  final FindingRepository _localFindingRepository = SqliteFindingRepository();
  final SiteRepository _localSiteRepository = SqliteSiteRepository();
  final AreaRepository _localAreaRepository = SqliteAreaRepository();

  // Remote repository instances
  final UserRepository _remoteUserRepository = FirebaseUserRepository();

  /// Get user repository based on connectivity
  Future<UserRepository> getUserRepository() async {
    final hasConnection = await _hasConnectivity();
    return hasConnection ? _remoteUserRepository : _localUserRepository;
  }

  /// Get session repository (always local for offline-first approach)
  SessionRepository getSessionRepository() {
    return _localSessionRepository;
  }

  /// Get finding repository (always local for offline-first approach)
  FindingRepository getFindingRepository() {
    return _localFindingRepository;
  }

  /// Get site repository (always local for offline-first approach)
  SiteRepository getSiteRepository() {
    return _localSiteRepository;
  }

  /// Get area repository (always local for offline-first approach)
  AreaRepository getAreaRepository() {
    return _localAreaRepository;
  }

  /// Get local user repository for caching and offline operations
  UserRepository getLocalUserRepository() {
    return _localUserRepository;
  }

  /// Get remote user repository for cloud operations
  UserRepository getRemoteUserRepository() {
    return _remoteUserRepository;
  }

  /// Check if device has internet connectivity
  Future<bool> _hasConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      // If connectivity check fails, assume offline
      return false;
    }
  }

  /// Create hybrid repository that uses both local and remote
  HybridUserRepository createHybridUserRepository() {
    return HybridUserRepository(_localUserRepository, _remoteUserRepository);
  }
}

/// Hybrid repository that manages both local and remote operations
class HybridUserRepository implements UserRepository {
  final UserRepository _localRepository;
  final UserRepository _remoteRepository;

  HybridUserRepository(this._localRepository, this._remoteRepository);

  @override
  Future<UserModel?> getUserById(String id) async {
    // Try local first, then remote
    UserModel? user = await _localRepository.getUserById(id);
    if (user == null && await _hasConnectivity()) {
      user = await _remoteRepository.getUserById(id);
      if (user != null) {
        // Cache locally
        await _localRepository.createUser(user);
      }
    }
    return user;
  }

  @override
  Future<UserModel?> getUserByEmail(String email) async {
    return await _localRepository.getUserByEmail(email);
  }

  @override
  Future<void> createUser(UserModel user) async {
    // Always save locally first
    await _localRepository.createUser(user);

    // Try to sync to remote if connected
    if (await _hasConnectivity()) {
      try {
        await _remoteRepository.createUser(user);
        await _localRepository.markUserAsSynced(user.id);
      } catch (e) {
        // Will be synced later by sync service
      }
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    // Always update locally first
    await _localRepository.updateUser(user);

    // Try to sync to remote if connected
    if (await _hasConnectivity()) {
      try {
        await _remoteRepository.updateUser(user);
        await _localRepository.markUserAsSynced(user.id);
      } catch (e) {
        // Will be synced later by sync service
      }
    }
  }

  @override
  Future<void> deleteUser(String id) async {
    await _localRepository.deleteUser(id);

    if (await _hasConnectivity()) {
      try {
        await _remoteRepository.deleteUser(id);
      } catch (e) {
        // Handle deletion sync separately
      }
    }
  }

  @override
  Future<List<UserModel>> getAllUsers() async {
    return await _localRepository.getAllUsers();
  }

  @override
  Future<void> cacheUserCredentials(String email, String password) async {
    await _localRepository.cacheUserCredentials(email, password);
  }

  @override
  Future<UserModel?> authenticateOffline(String email, String password) async {
    return await _localRepository.authenticateOffline(email, password);
  }

  @override
  Future<void> clearCachedCredentials() async {
    await _localRepository.clearCachedCredentials();
  }

  @override
  Future<List<UserModel>> getUnsyncedUsers() async {
    return await _localRepository.getUnsyncedUsers();
  }

  @override
  Future<void> markUserAsSynced(String id) async {
    await _localRepository.markUserAsSynced(id);
  }

  /// Check connectivity
  Future<bool> _hasConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      return false;
    }
  }
}
