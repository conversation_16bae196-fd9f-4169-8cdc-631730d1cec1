import '../models/area_model.dart';

/// Abstract repository interface for area data operations
abstract class AreaRepository {
  /// Get area by ID
  Future<AreaModel?> getAreaById(String id);

  /// Create new area
  Future<void> createArea(AreaModel area);

  /// Update existing area
  Future<void> updateArea(AreaModel area);

  /// Delete area by ID
  Future<void> deleteArea(String id);

  /// Get all areas for a site
  Future<List<AreaModel>> getAreasBySite(String siteId);

  /// Get all areas
  Future<List<AreaModel>> getAllAreas();

  /// Get areas that haven't been synced to remote storage
  Future<List<AreaModel>> getUnsyncedAreas();

  /// Mark area as synced
  Future<void> markAreaAsSynced(String id);

  /// Get areas count for a site
  Future<int> getAreasCountBySite(String siteId);

  /// Search areas by name
  Future<List<AreaModel>> searchAreasByName(String query);
}
