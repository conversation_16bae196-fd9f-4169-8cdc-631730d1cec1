/// Application constants
class AppConstants {
  // App information
  static const String appName = 'SafeStride';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Industrial Safety Inspection Platform';

  // Database constants
  static const String databaseName = 'safestride.db';
  static const int databaseVersion = 1;

  // Firebase collection names
  static const String usersCollection = 'users';
  static const String sitesCollection = 'sites';
  static const String areasCollection = 'areas';
  static const String sessionsCollection = 'sessions';
  static const String findingsCollection = 'findings';

  // Shared preferences keys
  static const String keyIsFirstLaunch = 'is_first_launch';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyOfflineMode = 'offline_mode';
  static const String keyThemeMode = 'theme_mode';

  // Network timeouts
  static const int connectionTimeoutSeconds = 30;
  static const int receiveTimeoutSeconds = 30;

  // Image constants
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const int imageQuality = 85;
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;

  // QR code constants
  static const int qrCodeSize = 200;
  static const String qrCodePrefix = 'safestride://';

  // Sync constants
  static const int maxRetryAttempts = 3;
  static const int syncIntervalMinutes = 15;
  static const int batchSizeLimit = 50;

  // Validation constants
  static const int minPasswordLength = 8;
  static const int maxDescriptionLength = 500;
  static const int maxNameLength = 100;

  // UI constants
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 8.0;
  static const double defaultElevation = 4.0;

  // Error messages
  static const String errorNetworkUnavailable = 'Network connection unavailable';
  static const String errorAuthenticationFailed = 'Authentication failed';
  static const String errorDataSyncFailed = 'Data synchronization failed';
  static const String errorInvalidInput = 'Invalid input provided';
  static const String errorUnknown = 'An unknown error occurred';

  // Success messages
  static const String successDataSaved = 'Data saved successfully';
  static const String successDataSynced = 'Data synchronized successfully';
  static const String successAccountCreated = 'Account created successfully';
  static const String successSignedIn = 'Signed in successfully';
}
