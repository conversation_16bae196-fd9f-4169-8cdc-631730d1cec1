import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/screens/login_screen.dart';
import 'package:safestride/services/auth_service.dart';
import 'package:safestride/models/user_model.dart';

// Generate mocks
@GenerateMocks([AuthService])
import 'login_screen_test.mocks.dart';

void main() {
  group('LoginScreen Widget Tests', () {
    late MockAuthService mockAuthService;

    setUp(() {
      mockAuthService = MockAuthService();

      // Set up default mock behavior
      when(mockAuthService.isLoading).thenReturn(false);
      when(mockAuthService.isOffline).thenReturn(false);
      when(mockAuthService.isAuthenticated).thenReturn(false);
      when(mockAuthService.currentUser).thenReturn(null);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthService>.value(
          value: mockAuthService,
          child: const LoginScreen(),
        ),
      );
    }

    testWidgets('should display app logo and title',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('SafeStride'), findsOneWidget);
      expect(find.text('Industrial Safety Inspection'), findsOneWidget);
      expect(find.byIcon(Icons.security), findsOneWidget);
    });

    testWidgets('should display email and password fields',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Email Address'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('should display sign in button', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should display SSO buttons when online',
        (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isOffline).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Continue with Google'), findsOneWidget);
    });

    testWidgets('should hide SSO buttons when offline',
        (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isOffline).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Continue with Google'), findsNothing);
      expect(find.text('SSO options are not available in offline mode'),
          findsOneWidget);
    });

    testWidgets('should display offline indicator when offline',
        (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isOffline).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Offline mode - Limited functionality available'),
          findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
    });

    testWidgets('should validate email field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act - Try to submit with empty email
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your email'), findsOneWidget);
    });

    testWidgets('should validate email format', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act - Enter invalid email format
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('should validate password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act - Enter valid email but no password
      await tester.enterText(
          find.byType(TextFormField).first, '<EMAIL>');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('should validate password length', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act - Enter short password
      await tester.enterText(
          find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, '123');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(
          find.text('Password must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('should toggle password visibility',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act - Find and tap the visibility toggle button
      final passwordField = find.byType(TextFormField).last;
      final visibilityButton = find.descendant(
        of: passwordField,
        matching: find.byType(IconButton),
      );

      await tester.tap(visibilityButton);
      await tester.pump();

      // Assert - The icon should change (this is a simplified test)
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should show loading indicator when signing in',
        (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isLoading).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    });

    testWidgets('should disable buttons when loading',
        (WidgetTester tester) async {
      // Arrange
      when(mockAuthService.isLoading).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final signInButton =
          tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(signInButton.onPressed, isNull);
    });

    testWidgets('should call auth service on sign in',
        (WidgetTester tester) async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      when(mockAuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      )).thenAnswer((_) async => UserModel(
            id: 'test-id',
            email: email,
            name: 'Test User',
            role: UserRole.observer,
            subscription: SubscriptionType.free,
            createdAt: DateTime.now(),
          ));

      await tester.pumpWidget(createTestWidget());

      // Act - Enter credentials and sign in
      await tester.enterText(find.byType(TextFormField).first, email);
      await tester.enterText(find.byType(TextFormField).last, password);
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      verify(mockAuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      )).called(1);
    });

    testWidgets('should display remember me checkbox',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Remember me'), findsOneWidget);
      expect(find.byType(Checkbox), findsOneWidget);
    });

    testWidgets('should display forgot password link',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Forgot Password?'), findsOneWidget);
    });

    testWidgets('should display sign up link', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text("Don't have an account? "), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('should be accessible with large fonts',
        (WidgetTester tester) async {
      // Arrange
      await tester.binding.setSurfaceSize(const Size(400, 800));

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<AuthService>.value(
            value: mockAuthService,
            child: MediaQuery(
              data: const MediaQueryData(textScaler: TextScaler.linear(2.0)),
              child: const LoginScreen(),
            ),
          ),
        ),
      );

      // Assert - Should not overflow
      expect(tester.takeException(), isNull);
    });

    testWidgets('should have minimum touch target sizes',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert - Check button sizes meet WCAG guidelines (44x44 minimum)
      final signInButton = tester.getSize(find.byType(ElevatedButton));
      expect(signInButton.height, greaterThanOrEqualTo(44));
    });
  });
}
