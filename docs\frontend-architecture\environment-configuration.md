# Environment Configuration

```dart
class Environment {
  static const String _environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  static bool get isDevelopment => _environment == 'development';
  static bool get isStaging => _environment == 'staging';
  static bool get isProduction => _environment == 'production';

  static String get firebaseProjectId {
    switch (_environment) {
      case 'production':
        return 'safestride-prod';
      case 'staging':
        return 'safestride-staging';
      default:
        return 'safestride-dev';
    }
  }
}
```
