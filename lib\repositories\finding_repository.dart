import '../models/finding_model.dart';

/// Abstract repository interface for finding operations
abstract class FindingRepository {
  /// Get finding by ID
  Future<FindingModel?> getFindingById(String id);

  /// Create new finding
  Future<void> createFinding(FindingModel finding);

  /// Update existing finding
  Future<void> updateFinding(FindingModel finding);

  /// Delete finding
  Future<void> deleteFinding(String id);

  /// Get findings by session ID
  Future<List<FindingModel>> getFindingsBySession(String sessionId);

  /// Get findings by author ID
  Future<List<FindingModel>> getFindingsByAuthor(String authorId);

  /// Get findings by severity
  Future<List<FindingModel>> getFindingsBySeverity(String severity);

  /// Get findings by category
  Future<List<FindingModel>> getFindingsByCategory(String category);

  /// Get all findings
  Future<List<FindingModel>> getAllFindings();

  /// Get findings that haven't been synced to cloud
  Future<List<FindingModel>> getUnsyncedFindings();

  /// Mark finding as synced
  Future<void> markFindingAsSynced(String id);

  /// Get count of findings by session
  Future<int> getFindingsCountBySession(String sessionId);

  /// Get count of findings by severity for a session
  Future<Map<String, int>> getFindingsCountBySeverity(String sessionId);
}
