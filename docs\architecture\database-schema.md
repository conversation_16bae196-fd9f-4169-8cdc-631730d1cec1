# Database Schema

### Firestore Collections

```
/users/{userId}
  - email: string
  - name: string
  - role: string
  - subscription: string
  - createdAt: timestamp

/sites/{siteId}
  - name: string
  - ownerId: string
  - areas: array<string>
  - createdAt: timestamp

/areas/{areaId}
  - name: string
  - siteId: string
  - description: string

/sessions/{sessionId}
  - type: string
  - leaderId: string
  - areaId: string
  - status: string
  - participants: array<string>
  - inviteCode: string
  - createdAt: timestamp
  - completedAt: timestamp?

/sessions/{sessionId}/findings/{findingId}
  - description: string
  - severity: string
  - category: string
  - photoUrl: string?
  - authorId: string
  - status: string
  - assignedTo: string?
  - createdAt: timestamp
  - resolvedAt: timestamp?
```

### SQLite Schema (Local Cache)

```sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  role TEXT,
  subscription TEXT,
  synced INTEGER DEFAULT 0
);

CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  leader_id TEXT,
  area_id TEXT,
  status TEXT,
  invite_code TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0
);

CREATE TABLE findings (
  id TEXT PRIMARY KEY,
  session_id TEXT,
  description TEXT,
  severity TEXT,
  category TEXT,
  photo_path TEXT,
  author_id TEXT,
  status TEXT,
  created_at INTEGER,
  synced INTEGER DEFAULT 0,
  FOREIGN KEY (session_id) REFERENCES sessions(id)
);
```

