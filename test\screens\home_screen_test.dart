import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/models/user_model.dart';
import 'package:safestride/providers/auth_provider.dart';
import 'package:safestride/screens/home_screen.dart';

import 'home_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  group('HomeScreen Widget Tests', () {
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = MockAuthProvider();
    });

    Widget createHomeScreen() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const HomeScreen(),
        ),
      );
    }

    testWidgets('should show loading indicator when user is null', (WidgetTester tester) async {
      when(mockAuthProvider.currentUser).thenReturn(null);

      await tester.pumpWidget(createHomeScreen());

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display welcome message with user name', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Welcome, John Doe'), findsOneWidget);
      expect(find.text('SafeStride'), findsOneWidget);
      expect(find.text('Industrial Safety Inspection Platform'), findsOneWidget);
    });

    testWidgets('should show Solo Walkabout button for Leader users', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Solo Walkabout'), findsOneWidget);
      expect(find.text('Conduct individual safety inspection'), findsOneWidget);
    });

    testWidgets('should not show Solo Walkabout button for Observer users', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Jane Doe',
        role: UserRole.observer,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Solo Walkabout'), findsNothing);
    });

    testWidgets('should show Group Walkabout button for all users', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.observer,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Group Walkabout'), findsOneWidget);
      expect(find.text('Join or create collaborative inspection'), findsOneWidget);
    });

    testWidgets('should show Export Data button', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Export Data'), findsOneWidget);
      expect(find.text('Export inspection data to CSV format'), findsOneWidget);
    });

    testWidgets('should display correct role information', (WidgetTester tester) async {
      final leaderUser = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(leaderUser);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Role: Leader'), findsOneWidget);
      expect(find.byIcon(Icons.admin_panel_settings), findsOneWidget);

      // Test observer role
      final observerUser = leaderUser.copyWith(role: UserRole.observer);
      when(mockAuthProvider.currentUser).thenReturn(observerUser);

      await tester.pumpWidget(createHomeScreen());
      await tester.pump();

      expect(find.text('Role: Observer'), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('should display subscription status', (WidgetTester tester) async {
      final freeUser = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(freeUser);

      await tester.pumpWidget(createHomeScreen());

      expect(find.text('Free Account'), findsOneWidget);
      expect(find.byIcon(Icons.star_border), findsOneWidget);

      // Test premium subscription
      final premiumUser = freeUser.copyWith(subscription: SubscriptionType.premium);
      when(mockAuthProvider.currentUser).thenReturn(premiumUser);

      await tester.pumpWidget(createHomeScreen());
      await tester.pump();

      expect(find.text('Premium Account'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
    });

    testWidgets('should show logout button in app bar', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      expect(find.byIcon(Icons.logout), findsOneWidget);
    });

    testWidgets('should show logout confirmation dialog when logout button is tapped', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      // Tap logout button
      await tester.tap(find.byIcon(Icons.logout));
      await tester.pumpAndSettle();

      expect(find.text('Sign Out'), findsNWidgets(2)); // Title and button
      expect(find.text('Are you sure you want to sign out?'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('should show coming soon dialog for Group Walkabout', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      // Tap Group Walkabout button
      await tester.tap(find.text('Group Walkabout'));
      await tester.pumpAndSettle();

      expect(find.text('Group Walkabout Coming Soon'), findsOneWidget);
      expect(find.text('Group Walkabout functionality will be available in a future update.'), findsOneWidget);
    });

    testWidgets('should have proper accessibility features', (WidgetTester tester) async {
      final user = UserModel(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'John Doe',
        role: UserRole.leader,
        subscription: SubscriptionType.free,
        createdAt: DateTime.now(),
      );

      when(mockAuthProvider.currentUser).thenReturn(user);

      await tester.pumpWidget(createHomeScreen());

      // Check for semantic labels and tooltips
      expect(find.byTooltip('Sign Out'), findsOneWidget);
      
      // Verify that action cards are tappable
      final soloWalkaboutCard = find.ancestor(
        of: find.text('Solo Walkabout'),
        matching: find.byType(InkWell),
      );
      expect(soloWalkaboutCard, findsOneWidget);
    });
  });
}
