import '../models/user_model.dart';

/// Abstract repository interface for user operations
abstract class UserRepository {
  /// Get user by ID
  Future<UserModel?> getUserById(String id);

  /// Get user by email
  Future<UserModel?> getUserByEmail(String email);

  /// Create new user
  Future<void> createUser(UserModel user);

  /// Update existing user
  Future<void> updateUser(UserModel user);

  /// Delete user
  Future<void> deleteUser(String id);

  /// Get all users
  Future<List<UserModel>> getAllUsers();

  /// Cache user credentials for offline authentication
  Future<void> cacheUserCredentials(String email, String password);

  /// Authenticate user offline using cached credentials
  Future<UserModel?> authenticateOffline(String email, String password);

  /// Clear cached credentials
  Future<void> clearCachedCredentials();

  /// Get users that haven't been synced to cloud
  Future<List<UserModel>> getUnsyncedUsers();

  /// Mark user as synced
  Future<void> markUserAsSynced(String id);
}
