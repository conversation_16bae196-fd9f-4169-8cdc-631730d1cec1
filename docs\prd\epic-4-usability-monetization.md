# Epic 4 Usability & Monetization
Implement multi-language support, Practice Mode, and freemium monetization to enhance accessibility and drive revenue.

## Story 4.1 [Low] Multi-Language & Practice Mode
As a user, I want to use the app in my preferred language and practice with sample data, so that I can effectively learn and adopt the safety inspection system.

### Acceptance Criteria
- 4.1.1: UI supports English, Spanish, Mandarin, Hindi, switchable in settings.
- 4.1.2: Practice Mode provides sample data for walkabout and review flows.
- 4.1.3: WCAG-compliant UI with large touch targets, high-contrast, color-blind-friendly.
- 4.1.4: E2E tests for language switching and Practice Mode.

## Story 4.2 [Low] Freemium Monetization
As a user, I want transparent pricing options with clear value propositions, so that I can choose the service level that best meets my safety inspection needs.

### Acceptance Criteria
- 4.2.1: Free tier limits: 2 Observers, 100MB storage, 3 AI tags/session, 1 Site.
- 4.2.2: Premium tier ($5/month or $50/year) unlocks unlimited Observers, 1TB storage, unlimited AI tags, multi-site support, PDF reports.
- 4.2.3: Subscription tab displays plans, trial prompt, and upgrade via `in_app_purchase`/`stripe_payment`.
- 4.2.4: Upselling prompts when limits exceeded (e.g., >2 Observers).
- 4.2.5: E2E tests for subscription flows.
