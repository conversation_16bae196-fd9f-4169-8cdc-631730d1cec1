import 'dart:io';
import 'package:flutter/material.dart';
import '../models/checklist_item_model.dart';
import '../services/photo_service.dart';
import '../utils/app_theme.dart';

/// Screen for conducting safety checklist inspection
class ChecklistScreen extends StatefulWidget {
  final String sessionId;
  final String areaName;
  final Function(List<ChecklistItemModel>) onChecklistCompleted;

  const ChecklistScreen({
    super.key,
    required this.sessionId,
    required this.areaName,
    required this.onChecklistCompleted,
  });

  @override
  State<ChecklistScreen> createState() => _ChecklistScreenState();
}

class _ChecklistScreenState extends State<ChecklistScreen> {
  late List<ChecklistItemModel> _checklistItems;
  final PhotoService _photoService = PhotoService();
  final Map<String, TextEditingController> _notesControllers = {};

  @override
  void initState() {
    super.initState();
    _checklistItems = DefaultChecklistItems.generateDefaultItems();
    
    // Initialize notes controllers
    for (final item in _checklistItems) {
      _notesControllers[item.id] = TextEditingController();
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    for (final controller in _notesControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final completedCount = _checklistItems.where((item) => item.isCompleted).length;
    final totalCount = _checklistItems.length;
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Scaffold(
      appBar: AppBar(
        title: Text('Checklist - ${widget.areaName}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress: $completedCount / $totalCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.white.withOpacity(0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _checklistItems.length,
        itemBuilder: (context, index) {
          final item = _checklistItems[index];
          return _buildChecklistItemCard(item, index);
        },
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: completedCount == totalCount ? _completeChecklist : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            completedCount == totalCount 
                ? 'Complete Checklist' 
                : 'Complete All Items ($completedCount/$totalCount)',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChecklistItemCard(ChecklistItemModel item, int index) {
    final controller = _notesControllers[item.id]!;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status indicator
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getStatusColor(item.status),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (item.isCompleted)
                  Icon(
                    item.isPassed ? Icons.check_circle : Icons.cancel,
                    color: item.isPassed ? Colors.green : Colors.red,
                    size: 24,
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              item.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Pass/Fail buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _updateItemStatus(item, ChecklistItemStatus.pass),
                    icon: const Icon(Icons.check),
                    label: const Text('Pass'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: item.status == ChecklistItemStatus.pass 
                          ? Colors.green 
                          : Colors.grey[300],
                      foregroundColor: item.status == ChecklistItemStatus.pass 
                          ? Colors.white 
                          : Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _updateItemStatus(item, ChecklistItemStatus.fail),
                    icon: const Icon(Icons.close),
                    label: const Text('Fail'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: item.status == ChecklistItemStatus.fail 
                          ? Colors.red 
                          : Colors.grey[300],
                      foregroundColor: item.status == ChecklistItemStatus.fail 
                          ? Colors.white 
                          : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Notes input
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Add any additional notes or observations...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              textCapitalization: TextCapitalization.sentences,
              onChanged: (value) => _updateItemNotes(item, value),
            ),
            
            const SizedBox(height: 16),
            
            // Photo section
            _buildPhotoSection(item),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoSection(ChecklistItemModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.camera_alt, size: 20),
            const SizedBox(width: 8),
            Text(
              'Photo Evidence',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            if (item.photoPath == null)
              TextButton.icon(
                onPressed: () => _addPhoto(item),
                icon: const Icon(Icons.add_a_photo),
                label: const Text('Add Photo'),
              ),
          ],
        ),
        
        if (item.photoPath != null) ...[
          const SizedBox(height: 8),
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(item.photoPath!),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(Icons.broken_image, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              TextButton.icon(
                onPressed: () => _replacePhoto(item),
                icon: const Icon(Icons.edit),
                label: const Text('Replace'),
              ),
              TextButton.icon(
                onPressed: () => _removePhoto(item),
                icon: const Icon(Icons.delete, color: Colors.red),
                label: const Text('Remove', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Color _getStatusColor(ChecklistItemStatus status) {
    switch (status) {
      case ChecklistItemStatus.pass:
        return Colors.green;
      case ChecklistItemStatus.fail:
        return Colors.red;
      case ChecklistItemStatus.notChecked:
        return Colors.grey;
    }
  }

  void _updateItemStatus(ChecklistItemModel item, ChecklistItemStatus status) {
    setState(() {
      final index = _checklistItems.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _checklistItems[index] = item.copyWith(
          status: status,
          completedAt: DateTime.now(),
        );
      }
    });
  }

  void _updateItemNotes(ChecklistItemModel item, String notes) {
    final index = _checklistItems.indexWhere((i) => i.id == item.id);
    if (index != -1) {
      _checklistItems[index] = item.copyWith(
        notes: notes.trim().isEmpty ? null : notes.trim(),
      );
    }
  }

  Future<void> _addPhoto(ChecklistItemModel item) async {
    final imagePath = await _photoService.showPhotoSourceDialog(context);
    if (imagePath != null) {
      setState(() {
        final index = _checklistItems.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _checklistItems[index] = item.copyWith(photoPath: imagePath);
        }
      });
    }
  }

  Future<void> _replacePhoto(ChecklistItemModel item) async {
    final imagePath = await _photoService.showPhotoSourceDialog(context);
    if (imagePath != null) {
      // Delete old photo if it exists
      if (item.photoPath != null) {
        await _photoService.deleteImage(item.photoPath!);
      }
      
      setState(() {
        final index = _checklistItems.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _checklistItems[index] = item.copyWith(photoPath: imagePath);
        }
      });
    }
  }

  Future<void> _removePhoto(ChecklistItemModel item) async {
    if (item.photoPath != null) {
      await _photoService.deleteImage(item.photoPath!);
      
      setState(() {
        final index = _checklistItems.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _checklistItems[index] = item.copyWith(photoPath: null);
        }
      });
    }
  }

  void _completeChecklist() {
    // Update notes from controllers
    for (final item in _checklistItems) {
      final controller = _notesControllers[item.id];
      if (controller != null && controller.text.trim().isNotEmpty) {
        final index = _checklistItems.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _checklistItems[index] = item.copyWith(
            notes: controller.text.trim(),
          );
        }
      }
    }
    
    widget.onChecklistCompleted(_checklistItems);
    Navigator.of(context).pop();
  }
}
