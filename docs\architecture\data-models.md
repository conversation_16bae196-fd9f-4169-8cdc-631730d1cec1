# Data Models

### User
**Purpose:** Represents system users with role-based access

**Key Attributes:**
- id: String - Unique Firebase Auth UID
- email: String - User email address
- name: String - Display name
- role: <PERSON><PERSON> (Leader, Observer) - Determines feature access
- subscription: Enum (Free, Premium) - Subscription tier
- createdAt: DateTime - Account creation timestamp

**Relationships:**
- One-to-many with <PERSON> (as creator or participant)
- One-to-many with Findings (as author)

### Site
**Purpose:** Represents physical locations for safety inspections

**Key Attributes:**
- id: String - Unique identifier
- name: String - Site display name
- ownerId: String - Reference to User
- areas: List<String> - References to Area IDs
- createdAt: DateTime - Creation timestamp

**Relationships:**
- Belongs to User (owner)
- One-to-many with Areas

### Area
**Purpose:** Specific zones within sites for targeted inspections

**Key Attributes:**
- id: String - Unique identifier
- name: String - Area display name
- siteId: String - Reference to parent Site
- description: String - Optional area description

### Session
**Purpose:** Individual or collaborative safety inspection instances

**Key Attributes:**
- id: String - Unique identifier
- type: Enum (Solo, Group) - Session type
- leaderId: String - Reference to leading User
- areaId: String - Reference to inspection Area
- status: Enum (Active, Completed, Cancelled) - Current state
- participants: List<String> - Observer User IDs
- inviteCode: String - QR code/link for joining
- createdAt: DateTime - Session start time
- completedAt: DateTime? - Session completion time

**Relationships:**
- Belongs to User (leader)
- Belongs to Area
- One-to-many with Findings
- Many-to-many with Users (participants)

### Finding
**Purpose:** Individual safety hazards or observations

**Key Attributes:**
- id: String - Unique identifier
- sessionId: String - Reference to parent Session
- authorId: String - Reference to User who created
- description: String - Hazard description
- severity: Enum (Low, Medium, High) - Risk level
- category: String - AI-suggested or manual category
- photoUrl: String? - Reference to stored image
- location: GeoPoint? - GPS coordinates if available
- status: Enum (Open, InProgress, Resolved) - Resolution state
- assignedTo: String? - User ID for follow-up
- createdAt: DateTime - Finding timestamp
- resolvedAt: DateTime? - Resolution timestamp

**Relationships:**
- Belongs to Session
- Belongs to User (author)
- May reference User (assignedTo)

