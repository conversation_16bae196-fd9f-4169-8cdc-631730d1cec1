# Core Workflows

### Solo Inspection Workflow
```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant S as SQLite
    participant F as Firestore
    
    U->>A: Start Solo Walkabout
    A->>S: Create local session
    U->>A: Complete checklist items
    A->>S: Store findings locally
    U->>A: Add hazard photos
    A->>S: Cache compressed images
    A->>A: Run AI hazard detection
    U->>A: Complete inspection
    A->>F: Sync session data (when online)
    A->>F: Upload photos to Cloud Storage
    F-->>A: Confirm sync completion
```

### Group Collaboration Workflow
```mermaid
sequenceDiagram
    participant L as Leader
    participant O as Observer
    participant A as App
    participant F as Firestore
    
    L->>A: Create Group Session
    A->>F: Store session with invite code
    L->>O: Share QR code/link
    O->>A: Scan QR code
    A->>F: Request to join session
    F-->>A: Notify Leader of join request
    L->>A: Approve Observer
    A->>F: Update session participants
    F-->>A: Real-time sync to Observer
    O->>A: Submit findings
    A->>F: Store findings with real-time updates
    F-->>A: Sync to all participants
    L->>A: Review and merge findings
    A->>F: Finalize session
```

