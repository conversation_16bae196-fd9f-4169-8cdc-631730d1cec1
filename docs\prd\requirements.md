# Requirements

## Functional Requirements (Priority: Critical/High/Medium/Low)

- **FR1 [Critical]**: Users must select their role (Leader or Observer) to access appropriate workflows and features for their safety inspection responsibilities.
- **FR2 [Critical]**: Leaders must be able to conduct individual safety inspections by completing standardized checklists, documenting hazards with photos and descriptions, and sharing findings with stakeholders.
- **FR3 [High]**: Leaders must be able to coordinate team-based safety inspections by inviting team members and managing collaborative inspection sessions.
- **FR4 [High]**: Observers must be able to join collaborative inspection sessions and contribute their findings to the shared inspection process.
- **FR5 [High]**: Users must receive automated assistance in categorizing safety hazards from photos to improve documentation speed and consistency.
- **FR6 [High]**: Leaders must be able to review all inspection findings, resolve duplicate entries, and assign follow-up actions to ensure hazard remediation.
- **FR7 [Medium]**: Premium users must be able to manage safety inspections across multiple facilities to support enterprise-scale operations.
- **FR8 [Medium]**: Users must be able to generate compliance-ready reports in standard formats to meet regulatory and audit requirements.
- **FR9 [Low]**: Users must be able to use the app in their preferred language and practice with sample data to improve adoption and usability.
- **FR10 [Medium]**: Leaders must be able to organize inspection locations hierarchically to support structured safety management processes.

## Non-Functional
- NFR1: The app must operate offline with SQLite caching, syncing to Firebase when online, supporting 85% of users in poor connectivity areas.
- NFR2: UI actions must complete in <1 second to ensure responsiveness for glove-wearing users.
- NFR3: Photos must be compressed to 250KB to support 100 findings/photos offline within storage limits (100MB Free, 1TB Premium).
- NFR4: The app must support iOS 12.0+ (iPhone 7+) and Android 8.0+ for broad device compatibility.
- NFR5: Data must be secured with HTTPS sync, encrypted SQLite, and Firestore security rules (e.g., Observers access own findings, Leaders access session data).
- NFR6: The app must handle 100 sessions/month without performance degradation.
- NFR7: AI models (MobileNetV2 <4MB, MiniLM <5MB) must run offline on-device for hazard tagging and duplicate detection.
