import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/screens/welcome_screen.dart';
import 'package:safestride/utils/app_theme.dart';

void main() {
  testWidgets('Welcome screen displays correctly', (WidgetTester tester) async {
    // Build the welcome screen without Firebase dependencies
    await tester.pumpWidget(
      MaterialApp(
        theme: AppTheme.lightTheme,
        home: const WelcomeScreen(),
      ),
    );

    // Verify that the welcome screen is displayed.
    expect(find.text('Welcome to SafeStride'), findsOneWidget);
    expect(find.text('Industrial Safety Inspection Platform'), findsOneWidget);

    // Verify that the main action buttons are present.
    expect(find.text('Sign In'), findsOneWidget);
    expect(find.text('Create Account'), findsOneWidget);
  });
}
