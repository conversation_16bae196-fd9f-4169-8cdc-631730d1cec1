import '../models/site_model.dart';

/// Abstract repository interface for site data operations
abstract class SiteRepository {
  /// Get site by ID
  Future<SiteModel?> getSiteById(String id);

  /// Create new site
  Future<void> createSite(SiteModel site);

  /// Update existing site
  Future<void> updateSite(SiteModel site);

  /// Delete site by ID
  Future<void> deleteSite(String id);

  /// Get all sites for a user
  Future<List<SiteModel>> getSitesByOwner(String ownerId);

  /// Get all sites
  Future<List<SiteModel>> getAllSites();

  /// Get sites that haven't been synced to remote storage
  Future<List<SiteModel>> getUnsyncedSites();

  /// Mark site as synced
  Future<void> markSiteAsSynced(String id);

  /// Search sites by name
  Future<List<SiteModel>> searchSitesByName(String query);

  /// Get default site for user (creates one if none exists)
  Future<SiteModel> getOrCreateDefaultSite(String ownerId);
}
