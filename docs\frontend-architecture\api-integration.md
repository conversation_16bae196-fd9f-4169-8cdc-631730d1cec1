# API Integration

## Service Template
```dart
class InspectionService {
  final FirebaseFirestore _firestore;
  final LocalDatabase _localDb;
  final ConnectivityService _connectivity;

  Future<List<Finding>> getFindings(String sessionId) async {
    try {
      // Try local first for offline support
      final localFindings = await _localDb.getFindings(sessionId);
      
      if (await _connectivity.isConnected()) {
        // Sync with remote if online
        final remoteFindings = await _firestore
            .collection('sessions')
            .doc(sessionId)
            .collection('findings')
            .get();
        
        // Merge and update local cache
        await _syncFindings(localFindings, remoteFindings);
      }
      
      return localFindings;
    } catch (e) {
      throw InspectionException('Failed to load findings: $e');
    }
  }
}
```

## Client Configuration
```dart
class ApiClient {
  static const Duration _timeout = Duration(seconds: 30);
  static const int _maxRetries = 3;

  final FirebaseFirestore _firestore;
  final ConnectivityService _connectivity;

  Future<T> executeWithRetry<T>(Future<T> Function() operation) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        return await operation().timeout(_timeout);
      } catch (e) {
        if (attempt == _maxRetries || !_shouldRetry(e)) rethrow;
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
    throw Exception('Max retries exceeded');
  }
}
```
