# SafeStride

Offline-first mobile app for digitising workplace safety walkabouts.

## Development Setup
1. `flutter pub get`
2. Copy `.env.sample` → `.env` and fill Firebase keys.
3. Run `flutter run --dart-define-from-file=.env`

## Implementation Checklist (Practical & Essential Solutions)
- [ ] Offline sync queue + retry/backoff implemented (`lib/data/sync_worker.dart`)
- [ ] Basic conflict resolution (last-write-wins) in place
- [ ] Model update checker uses `ai_models` collection
- [ ] Image compression (<250 KB) before upload
- [ ] Batched Firestore writes & limited listeners
- [ ] Firestore usage alerts configured
- [ ] Firebase Test Lab workflow in GitHub Actions
- [ ] Repository/service abstraction layer for Firebase
- [ ] CSV export enabled & BigQuery export toggled
- [ ] App Check + Security Rules deployed
- [ ] In-app data access/deletion controls live

Tick items as you implement.

## Scripts
| Command | Purpose |
|---------|---------|
| `npm run sync-test` | Simulate offline ↔ online transitions |
| `npm run model-update` | Download latest TFLite models |

---

For architecture, API, and database details see:
- `TechnicalArchitecture.md`
- `APISpecification.md`
- `DatabaseDesign.md`
