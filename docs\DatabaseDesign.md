# SafeStride Database Design

**Datastores:**
1. **SQLite (encrypted)** – offline cache & sync queue.  
2. **Firestore** – cloud persistence, real-time updates.  
3. **Firebase Storage** – image & model binaries.

---

## 1. SQLite Schema
### 1.1 Core Tables
| Table | Purpose | Key Fields |
|-------|---------|-----------|
| `users` | basic profile cache | uid (PK), name, role, email |
| `areas` | walkabout locations | id (PK), site_id, name, updated_at |
| `sessions` | walkabout sessions | id (PK), area_id, leader_id, started_at, status |
| `findings` | hazards/checklist items | id (PK), session_id, type, desc, severity, photo_path, updated_at |

### 1.2 Sync Queue (see API spec)
Used by background worker for cloud sync.

### 1.3 Audit Log (lightweight)
Optional `audit_log` table records local actions for debugging: `{id, entity, entity_id, action, ts}`.

Encryption handled via SQLCipher; DB key derived from Firebase UID + device ID.

---

## 2. Firestore Schema (Cloud)
Mirror of SQLite entities with additional indexing:
- `users/{uid}`
- `sites/{siteId}`
  - `areas/{areaId}`
    - `sessions/{sessionId}`
      - `findings/{findingId}`

**Composite Indexes:**
- `findings` by `session_id + updated_at` (for Review Hub ordering)
- `sessions` by `leader_id + started_at` (for history list)

---

## 3. Data Retention & Archiving
- **Active Data:** retained indefinitely while account active.
- **Archive:** Cloud Function moves sessions older than 3 years to Coldline Storage; deletes `photo_thumb` to reduce reads.
- **Deletion:** `purgeUserData` function deletes all docs & storage for UID on request.

---

*This design supports the essential offline-first and scalability requirements.*
