import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/models/checklist_item_model.dart';

void main() {
  group('ChecklistItemModel', () {
    test('should create ChecklistItemModel with required fields', () {
      final item = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
      );

      expect(item.id, 'test-id');
      expect(item.title, 'Test Item');
      expect(item.description, 'Test description');
      expect(item.status, ChecklistItemStatus.notChecked);
      expect(item.notes, isNull);
      expect(item.photoPath, isNull);
      expect(item.completedAt, isNull);
      expect(item.isCompleted, isFalse);
      expect(item.isPassed, isFalse);
      expect(item.isFailed, isFalse);
    });

    test('should create ChecklistItemModel with all fields', () {
      final completedAt = DateTime(2024, 1, 1);
      final item = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
        status: ChecklistItemStatus.pass,
        notes: 'Test notes',
        photoPath: '/path/to/photo.jpg',
        completedAt: completedAt,
      );

      expect(item.id, 'test-id');
      expect(item.title, 'Test Item');
      expect(item.description, 'Test description');
      expect(item.status, ChecklistItemStatus.pass);
      expect(item.notes, 'Test notes');
      expect(item.photoPath, '/path/to/photo.jpg');
      expect(item.completedAt, completedAt);
      expect(item.isCompleted, isTrue);
      expect(item.isPassed, isTrue);
      expect(item.isFailed, isFalse);
    });

    test('should correctly identify failed status', () {
      final item = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
        status: ChecklistItemStatus.fail,
      );

      expect(item.isCompleted, isTrue);
      expect(item.isPassed, isFalse);
      expect(item.isFailed, isTrue);
    });

    test('should convert to map correctly', () {
      final completedAt = DateTime(2024, 1, 1);
      final item = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
        status: ChecklistItemStatus.pass,
        notes: 'Test notes',
        photoPath: '/path/to/photo.jpg',
        completedAt: completedAt,
      );

      final map = item.toMap();

      expect(map['id'], 'test-id');
      expect(map['title'], 'Test Item');
      expect(map['description'], 'Test description');
      expect(map['status'], 'pass');
      expect(map['notes'], 'Test notes');
      expect(map['photo_path'], '/path/to/photo.jpg');
      expect(map['completed_at'], completedAt.millisecondsSinceEpoch);
    });

    test('should create from map correctly', () {
      final completedAt = DateTime(2024, 1, 1);
      final map = {
        'id': 'test-id',
        'title': 'Test Item',
        'description': 'Test description',
        'status': 'pass',
        'notes': 'Test notes',
        'photo_path': '/path/to/photo.jpg',
        'completed_at': completedAt.millisecondsSinceEpoch,
      };

      final item = ChecklistItemModel.fromMap(map);

      expect(item.id, 'test-id');
      expect(item.title, 'Test Item');
      expect(item.description, 'Test description');
      expect(item.status, ChecklistItemStatus.pass);
      expect(item.notes, 'Test notes');
      expect(item.photoPath, '/path/to/photo.jpg');
      expect(item.completedAt, completedAt);
    });

    test('should create from map with string date', () {
      final map = {
        'id': 'test-id',
        'title': 'Test Item',
        'description': 'Test description',
        'status': 'fail',
        'notes': null,
        'photo_path': null,
        'completed_at': '2024-01-01T00:00:00.000Z',
      };

      final item = ChecklistItemModel.fromMap(map);

      expect(item.id, 'test-id');
      expect(item.title, 'Test Item');
      expect(item.description, 'Test description');
      expect(item.status, ChecklistItemStatus.fail);
      expect(item.notes, isNull);
      expect(item.photoPath, isNull);
      expect(item.completedAt, DateTime.parse('2024-01-01T00:00:00.000Z'));
    });

    test('should handle invalid status gracefully', () {
      final map = {
        'id': 'test-id',
        'title': 'Test Item',
        'description': 'Test description',
        'status': 'invalid_status',
        'notes': null,
        'photo_path': null,
        'completed_at': null,
      };

      final item = ChecklistItemModel.fromMap(map);

      expect(item.status, ChecklistItemStatus.notChecked);
    });

    test('should copy with updated fields', () {
      final original = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
      );

      final completedAt = DateTime(2024, 1, 1);
      final updated = original.copyWith(
        status: ChecklistItemStatus.pass,
        notes: 'Updated notes',
        photoPath: '/new/path.jpg',
        completedAt: completedAt,
      );

      expect(updated.id, 'test-id');
      expect(updated.title, 'Test Item');
      expect(updated.description, 'Test description');
      expect(updated.status, ChecklistItemStatus.pass);
      expect(updated.notes, 'Updated notes');
      expect(updated.photoPath, '/new/path.jpg');
      expect(updated.completedAt, completedAt);
    });

    test('should implement equality correctly', () {
      final item1 = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
      );

      final item2 = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
      );

      final item3 = ChecklistItemModel(
        id: 'different-id',
        title: 'Test Item',
        description: 'Test description',
      );

      expect(item1, equals(item2));
      expect(item1, isNot(equals(item3)));
      expect(item1.hashCode, equals(item2.hashCode));
      expect(item1.hashCode, isNot(equals(item3.hashCode)));
    });

    test('should convert to string correctly', () {
      final item = ChecklistItemModel(
        id: 'test-id',
        title: 'Test Item',
        description: 'Test description',
        status: ChecklistItemStatus.pass,
        notes: 'Test notes',
        photoPath: '/path/to/photo.jpg',
        completedAt: DateTime(2024, 1, 1),
      );

      final string = item.toString();

      expect(string, contains('test-id'));
      expect(string, contains('Test Item'));
      expect(string, contains('Test description'));
      expect(string, contains('pass'));
      expect(string, contains('Test notes'));
      expect(string, contains('/path/to/photo.jpg'));
    });
  });

  group('DefaultChecklistItems', () {
    test('should have 10 predefined items', () {
      expect(DefaultChecklistItems.items.length, 10);
    });

    test('should generate default items with unique IDs', () {
      final items = DefaultChecklistItems.generateDefaultItems();
      
      expect(items.length, 10);
      
      // Check that all IDs are unique
      final ids = items.map((item) => item.id).toSet();
      expect(ids.length, 10);
      
      // Check that all items have the expected structure
      for (int i = 0; i < items.length; i++) {
        final item = items[i];
        expect(item.id, 'default_${i + 1}');
        expect(item.title, isNotEmpty);
        expect(item.description, isNotEmpty);
        expect(item.status, ChecklistItemStatus.notChecked);
        expect(item.notes, isNull);
        expect(item.photoPath, isNull);
        expect(item.completedAt, isNull);
      }
    });

    test('should include expected safety categories', () {
      final items = DefaultChecklistItems.generateDefaultItems();
      final titles = items.map((item) => item.title).toList();
      
      expect(titles, contains('Emergency Exits'));
      expect(titles, contains('Fire Safety Equipment'));
      expect(titles, contains('Personal Protective Equipment'));
      expect(titles, contains('Electrical Safety'));
      expect(titles, contains('Housekeeping & Cleanliness'));
      expect(titles, contains('Chemical Storage'));
      expect(titles, contains('Machinery & Equipment'));
      expect(titles, contains('Ventilation Systems'));
      expect(titles, contains('Safety Signage'));
      expect(titles, contains('First Aid Facilities'));
    });
  });
}
