rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Sites collection - authenticated users can read, only leaders can write
    match /sites/{siteId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'leader';
    }
    
    // Areas collection - authenticated users can read, only leaders can write
    match /areas/{areaId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'leader';
    }
    
    // Sessions collection - authenticated users can read sessions they're part of
    match /sessions/{sessionId} {
      allow read: if request.auth != null && (
        resource.data.leaderId == request.auth.uid ||
        request.auth.uid in resource.data.participants
      );
      allow write: if request.auth != null && 
        resource.data.leaderId == request.auth.uid;
      
      // Findings subcollection
      match /findings/{findingId} {
        allow read: if request.auth != null && (
          get(/databases/$(database)/documents/sessions/$(sessionId)).data.leaderId == request.auth.uid ||
          request.auth.uid in get(/databases/$(database)/documents/sessions/$(sessionId)).data.participants
        );
        allow write: if request.auth != null && (
          get(/databases/$(database)/documents/sessions/$(sessionId)).data.leaderId == request.auth.uid ||
          request.auth.uid in get(/databases/$(database)/documents/sessions/$(sessionId)).data.participants
        );
      }
    }
    
    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
