# Coding Standards

### Core Standards
- **Dart Style Guide:** Official Dart formatting and conventions
- **Null Safety:** Strict null safety enabled
- **Documentation:** Comprehensive dartdoc comments for public APIs
- **Testing:** Minimum 80% code coverage for business logic

### Naming Conventions
- **Classes:** PascalCase (UserRepository, SessionService)
- **Variables/Functions:** camelCase (getCurrentUser, sessionId)
- **Constants:** SCREAMING_SNAKE_CASE (MAX_PHOTO_SIZE)
- **Files:** snake_case (user_repository.dart, session_service.dart)

### Critical Rules
1. **No direct Firebase calls in UI:** Use repository pattern
2. **Always handle offline state:** Check connectivity before network calls
3. **Validate all user inputs:** Client and server-side validation
4. **Encrypt sensitive data:** Use secure storage for credentials
5. **Log errors appropriately:** Include context but protect privacy

