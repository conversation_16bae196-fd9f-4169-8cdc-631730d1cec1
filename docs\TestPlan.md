# SafeStride Test Plan

**Scope:** Covers practical & essential solutions.

## 1. Unit Tests
- Service layer (Firestore/SQLite abstraction)
- Sync queue enqueue/dequeue logic
- Image compression function outputs size ≤ 250 KB
- Model version checker compares semantic versions correctly

## 2. Integration Tests
- Offline → online sync with conflict resolution
- Batched Firestore write success & retry on failure (mock)
- Model download & checksum validation

## 3. E2E (Flutter integration_test)
| Flow | Devices | Case |
|------|---------|------|
| Login & Role Select | Pixel 4a, iPhone 8 | Validate credential caching offline |
| Solo Walkabout | Pixel 4a | Create checklist, sync when online |
| Group Walkabout | iPhone 8 | Invite Observer, submit findings |
| Export CSV | Pixel 4a | Generate + share file |

## 4. Device Farm
- Firebase Test Lab matrix: API 30/33, iOS 14/16

## 5. Network Simulation
- Use `NetworkPolicy` in integration tests: offline 30 s → 4G → offline.

## 6. Performance Targets
- UI response < 1 s (integration perf test)
- Sync queue flush ≤ 3 s for 500 writes

## 7. Accessibility
- Flutter a11y CI check (`flutter_a11y`) in pipeline

---

Update this plan as new features are added.
